This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
models/
  amount_candidate.dart
  localization_data.dart
  parse_result.dart
  transaction_model.dart
navigation/
  app_navigation.dart
screens/
  categories_screen.dart
  chat_screen.dart
  settings_screen.dart
  statistics_screen.dart
services/
  parser/
    category_finder_service.dart
    category_keyword_map.dart
    entity_extractor_base.dart
    fallback_parser_service.dart
    learned_association_service.dart
    learned_category_storage.dart
    mlkit_parser_service.dart
    real_entity_extractor.dart
  localization_service.dart
  startup_service.dart
  storage_service.dart
  transaction_parser_service.dart
utils/
  amount_utils.dart
  currency_utils.dart
  raw_number_finder.dart
widgets/
  category_picker_dialog.dart
  quick_reply_widget.dart
  startup_loading_widget.dart
  transaction_edit_dialog.dart
  transaction_message.dart
audio_recorder.dart
file_upload.dart
image_upload.dart
main.dart
theme.dart
video_recorder.dart
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="models/amount_candidate.dart">
/// Enum representing the source of an amount candidate
enum AmountSource {
  mlKit,
  rawNumberFinder,
}

/// Data class representing a potential amount found in text
/// This standardizes how we represent amount candidates from different sources
class AmountCandidate {
  final double amount;
  final String? currency;
  final int start;
  final int end;
  final String sourceText;
  final AmountSource source;

  const AmountCandidate({
    required this.amount,
    this.currency,
    required this.start,
    required this.end,
    required this.sourceText,
    required this.source,
  });

  /// Create an AmountCandidate from ML Kit entity
  factory AmountCandidate.fromMLKit({
    required double amount,
    String? currency,
    required int start,
    required int end,
    required String sourceText,
  }) {
    return AmountCandidate(
      amount: amount,
      currency: currency,
      start: start,
      end: end,
      sourceText: sourceText,
      source: AmountSource.mlKit,
    );
  }

  /// Create an AmountCandidate from raw number finder
  factory AmountCandidate.fromRawNumberFinder({
    required double amount,
    String? currency,
    required int start,
    required int end,
    required String sourceText,
  }) {
    return AmountCandidate(
      amount: amount,
      currency: currency,
      start: start,
      end: end,
      sourceText: sourceText,
      source: AmountSource.rawNumberFinder,
    );
  }

  /// Convert to Map for backward compatibility with existing code
  Map<String, dynamic> toMap() {
    return {
      'amount': amount,
      'currency': currency,
      'start': start,
      'end': end,
      'sourceText': sourceText,
      'source': source.name,
    };
  }

  /// Create AmountCandidate from Map for backward compatibility
  factory AmountCandidate.fromMap(Map<String, dynamic> map) {
    return AmountCandidate(
      amount: (map['amount'] as num).toDouble(),
      currency: map['currency'] as String?,
      start: map['start'] as int,
      end: map['end'] as int,
      sourceText: map['sourceText'] as String,
      source: AmountSource.values.byName(map['source'] as String),
    );
  }

  /// Equality comparison based on amount, start, and end positions
  /// Two candidates are considered equal if they have the same amount
  /// and overlap in position (within tolerance)
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AmountCandidate) return false;

    // Consider candidates equal if they have the same amount
    // and their positions overlap (within a small tolerance)
    const tolerance = 0.01; // For floating point comparison
    const positionTolerance = 2; // Allow small position differences

    final amountMatch = (amount - other.amount).abs() < tolerance;
    final positionOverlap = (start - other.start).abs() <= positionTolerance ||
                           (end - other.end).abs() <= positionTolerance ||
                           (start <= other.end && end >= other.start);

    return amountMatch && positionOverlap;
  }

  @override
  int get hashCode {
    // Use amount rounded to avoid floating point precision issues
    // Use broader position grouping to match equality logic with tolerance
    final roundedAmount = (amount * 100).round();
    final normalizedStart = start ~/ 5; // Group positions with broader tolerance
    final normalizedEnd = end ~/ 5;
    return Object.hash(roundedAmount, normalizedStart, normalizedEnd);
  }

  /// String representation for debugging
  @override
  String toString() {
    final currencyStr = currency != null ? ' $currency' : '';
    return 'AmountCandidate(amount: $amount$currencyStr, '
           'position: $start-$end, text: "$sourceText", source: ${source.name})';
  }

  /// Create a copy with updated fields
  AmountCandidate copyWith({
    double? amount,
    String? currency,
    int? start,
    int? end,
    String? sourceText,
    AmountSource? source,
  }) {
    return AmountCandidate(
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      start: start ?? this.start,
      end: end ?? this.end,
      sourceText: sourceText ?? this.sourceText,
      source: source ?? this.source,
    );
  }

  /// Check if this candidate represents the same amount as another
  /// (ignoring position and source)
  bool hasSameAmount(AmountCandidate other) {
    const tolerance = 0.01;
    return (amount - other.amount).abs() < tolerance;
  }

  /// Check if this candidate's position overlaps with another
  bool overlapsPosition(AmountCandidate other) {
    return start <= other.end && end >= other.start;
  }

  /// Get the text span that this candidate covers
  String getTextSpan(String fullText) {
    if (start < 0 || end > fullText.length || start >= end) {
      return sourceText; // Fallback to stored source text
    }
    return fullText.substring(start, end);
  }
}
</file>

<file path="models/localization_data.dart">
import 'dart:convert';

/// Data model representing localization patterns for transaction parsing
class LocalizationData {
  final String locale;
  final String decimalSeparator;
  final String thousandsSeparator;
  final List<String> expenseKeywords;
  final List<String> incomeKeywords;
  final List<String> loanKeywords;
  final List<String> currencySymbols;
  final Map<String, String> specialPatterns;

  const LocalizationData({
    required this.locale,
    required this.decimalSeparator,
    required this.thousandsSeparator,
    required this.expenseKeywords,
    required this.incomeKeywords,
    required this.loanKeywords,
    required this.currencySymbols,
    required this.specialPatterns,
  });

  /// Creates LocalizationData from JSON map
  factory LocalizationData.fromJson(Map<String, dynamic> json) {
    try {
      // Validate required fields
      final locale = json['locale'] as String?;
      if (locale == null || locale.isEmpty) {
        throw FormatException('Missing or empty locale field');
      }

      final decimalSeparator = json['decimal_separator'] as String?;
      if (decimalSeparator == null || decimalSeparator.isEmpty) {
        throw FormatException('Missing or empty decimal_separator field');
      }

      final thousandsSeparator = json['thousands_separator'] as String?;
      if (thousandsSeparator == null || thousandsSeparator.isEmpty) {
        throw FormatException('Missing or empty thousands_separator field');
      }

      // Parse keyword arrays with validation
      final expenseKeywords = _parseStringList(json['expense_keywords'], 'expense_keywords');
      final incomeKeywords = _parseStringList(json['income_keywords'], 'income_keywords');
      final loanKeywords = _parseStringList(json['loan_keywords'], 'loan_keywords');
      final currencySymbols = _parseStringList(json['currency_symbols'], 'currency_symbols');

      // Parse special patterns map
      final specialPatternsRaw = json['special_patterns'] as Map<String, dynamic>?;
      final specialPatterns = <String, String>{};
      if (specialPatternsRaw != null) {
        for (final entry in specialPatternsRaw.entries) {
          if (entry.value is String) {
            specialPatterns[entry.key] = entry.value as String;
          }
        }
      }

      return LocalizationData(
        locale: locale,
        decimalSeparator: decimalSeparator,
        thousandsSeparator: thousandsSeparator,
        expenseKeywords: expenseKeywords,
        incomeKeywords: incomeKeywords,
        loanKeywords: loanKeywords,
        currencySymbols: currencySymbols,
        specialPatterns: specialPatterns,
      );
    } catch (e) {
      throw FormatException('Failed to parse LocalizationData: $e');
    }
  }

  /// Creates LocalizationData from JSON string
  factory LocalizationData.fromJsonString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return LocalizationData.fromJson(json);
    } catch (e) {
      throw FormatException('Failed to parse JSON string: $e');
    }
  }

  /// Helper method to parse and validate string lists
  static List<String> _parseStringList(dynamic value, String fieldName) {
    if (value == null) {
      throw FormatException('Missing $fieldName field');
    }

    if (value is! List) {
      throw FormatException('$fieldName must be a list');
    }

    final result = <String>[];
    for (int i = 0; i < value.length; i++) {
      final item = value[i];
      if (item is! String) {
        throw FormatException('$fieldName[$i] must be a string, got ${item.runtimeType}');
      }
      if (item.isEmpty) {
        throw FormatException('$fieldName[$i] cannot be empty');
      }
      result.add(item);
    }

    if (result.isEmpty) {
      throw FormatException('$fieldName cannot be empty');
    }

    return result;
  }

  /// Converts LocalizationData to JSON map
  Map<String, dynamic> toJson() {
    return {
      'locale': locale,
      'decimal_separator': decimalSeparator,
      'thousands_separator': thousandsSeparator,
      'expense_keywords': expenseKeywords,
      'income_keywords': incomeKeywords,
      'loan_keywords': loanKeywords,
      'currency_symbols': currencySymbols,
      'special_patterns': specialPatterns,
    };
  }

  /// Converts LocalizationData to JSON string
  String toJsonString() {
    return jsonEncode(toJson());
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LocalizationData) return false;

    return locale == other.locale &&
        decimalSeparator == other.decimalSeparator &&
        thousandsSeparator == other.thousandsSeparator &&
        _listEquals(expenseKeywords, other.expenseKeywords) &&
        _listEquals(incomeKeywords, other.incomeKeywords) &&
        _listEquals(loanKeywords, other.loanKeywords) &&
        _listEquals(currencySymbols, other.currencySymbols) &&
        _mapEquals(specialPatterns, other.specialPatterns);
  }

  @override
  int get hashCode {
    return Object.hash(
      locale,
      decimalSeparator,
      thousandsSeparator,
      Object.hashAll(expenseKeywords),
      Object.hashAll(incomeKeywords),
      Object.hashAll(loanKeywords),
      Object.hashAll(currencySymbols),
      Object.hashAll(specialPatterns.entries.map((e) => Object.hash(e.key, e.value))),
    );
  }

  @override
  String toString() {
    return 'LocalizationData(locale: $locale, expenseKeywords: ${expenseKeywords.length}, '
        'incomeKeywords: ${incomeKeywords.length}, loanKeywords: ${loanKeywords.length})';
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// Helper method to compare maps
  bool _mapEquals<K, V>(Map<K, V> a, Map<K, V> b) {
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}
</file>

<file path="models/parse_result.dart">
import 'transaction_model.dart';

/// Enum representing the status of parsing results
enum ParseStatus {
  success,
  needsCategory,
  needsType,
  needsAmountConfirmation,
  failed
}

/// Data transfer object for communicating parsing results between the ML Kit parser and the UI
class ParseResult {
  final Transaction transaction;
  final ParseStatus status;
  final String? error;
  final List<double>? candidateAmounts;
  final List<String>? candidateTexts;

  const ParseResult({
    required this.transaction,
    required this.status,
    this.error,
    this.candidateAmounts,
    this.candidateTexts,
  });

  /// Factory constructor for successful parsing without user input needed
  factory ParseResult.success(Transaction transaction) {
    return ParseResult(
      transaction: transaction,
      status: ParseStatus.success,
    );
  }

  /// Factory constructor for successful parsing but category selection needed
  factory ParseResult.needsCategory(Transaction transaction) {
    return ParseResult(
      transaction: transaction,
      status: ParseStatus.needsCategory,
    );
  }

  /// Factory constructor for successful parsing but transaction type selection needed
  factory ParseResult.needsType(Transaction partialTransaction) {
    return ParseResult(
      transaction: partialTransaction,
      status: ParseStatus.needsType,
    );
  }

  /// Factory constructor for successful parsing but amount confirmation needed
  factory ParseResult.needsAmountConfirmation(
    Transaction partialTransaction,
    List<double> candidateAmounts,
    List<String> candidateTexts,
  ) {
    return ParseResult(
      transaction: partialTransaction,
      status: ParseStatus.needsAmountConfirmation,
      candidateAmounts: candidateAmounts,
      candidateTexts: candidateTexts,
    );
  }

  /// Factory constructor for failed parsing
  factory ParseResult.failed(Transaction fallbackTransaction, String error) {
    return ParseResult(
      transaction: fallbackTransaction,
      status: ParseStatus.failed,
      error: error,
    );
  }

  /// Helper method to check if parsing was successful
  bool get isSuccess => status == ParseStatus.success && error == null;

  /// Helper method to check if user input is required
  bool get requiresUserInput => status == ParseStatus.needsCategory ||
                               status == ParseStatus.needsType ||
                               status == ParseStatus.needsAmountConfirmation;

  /// Helper method to check if there was an error
  bool get hasError => error != null;

  /// Helper method to check if category selection is needed
  bool get needsCategorySelection => status == ParseStatus.needsCategory;

  /// Helper method to check if transaction type selection is needed
  bool get needsTypeSelection => status == ParseStatus.needsType;

  /// Helper method to check if amount confirmation is needed
  bool get needsAmountConfirmation => status == ParseStatus.needsAmountConfirmation;

  @override
  String toString() {
    return 'ParseResult{transaction: $transaction, status: $status, error: $error, candidateAmounts: $candidateAmounts, candidateTexts: $candidateTexts}';
  }
}
</file>

<file path="models/transaction_model.dart">
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import '../services/storage_service.dart';
import '../services/parser/learned_association_service.dart';
import '../utils/currency_utils.dart';

enum TransactionType { expense, income, loan }

class Category {
  final String id;
  final String name;
  final String icon;
  final int colorValue;
  final TransactionType type;

  Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.colorValue,
    required this.type,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
      icon: json['icon'],
      colorValue: json['colorValue'],
      type: TransactionType.values.byName(json['type']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'colorValue': colorValue,
      'type': type.name,
    };
  }
}

class Transaction {
  final String id;
  final double amount;
  final TransactionType type;
  final String categoryId;
  final DateTime date;
  final String description;
  final List<String> tags;
  final String currencyCode;

  Transaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.categoryId,
    required this.date,
    required this.description,
    this.tags = const [],
    this.currencyCode = 'USD',
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      amount: json['amount'].toDouble(),
      type: TransactionType.values.byName(json['type']),
      categoryId: json['categoryId'],
      date: DateTime.parse(json['date']),
      description: json['description'],
      tags: List<String>.from(json['tags'] ?? []),
      currencyCode: json['currencyCode'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type.name,
      'categoryId': categoryId,
      'date': date.toIso8601String(),
      'description': description,
      'tags': tags,
      'currencyCode': currencyCode,
    };
  }

  // Create a copy of the transaction with updated fields
  Transaction copyWith({
    String? id,
    double? amount,
    TransactionType? type,
    String? categoryId,
    DateTime? date,
    String? description,
    List<String>? tags,
    String? currencyCode,
  }) {
    return Transaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      date: date ?? this.date,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      currencyCode: currencyCode ?? this.currencyCode,
    );
  }
}

/// Enum representing different types of chat messages
enum ChatMessageType {
  user,
  system,
  systemWithQuickReplies
}

class ChatMessage {
  final String id;
  final String text;
  final DateTime timestamp;
  final bool isUserMessage;
  final String? associatedTransactionId;
  final ChatMessageType type;
  final List<String>? quickReplies;
  final String? quickReplyId;

  ChatMessage({
    required this.id,
    required this.text,
    required this.timestamp,
    required this.isUserMessage,
    this.associatedTransactionId,
    ChatMessageType? type,
    this.quickReplies,
    this.quickReplyId,
  }) : type = type ?? (isUserMessage ? ChatMessageType.user : ChatMessageType.system);

  /// Factory constructor for user messages
  factory ChatMessage.user({
    required String id,
    required String text,
    required DateTime timestamp,
    String? associatedTransactionId,
  }) {
    return ChatMessage(
      id: id,
      text: text,
      timestamp: timestamp,
      isUserMessage: true,
      associatedTransactionId: associatedTransactionId,
      type: ChatMessageType.user,
    );
  }

  /// Factory constructor for regular system messages
  factory ChatMessage.system({
    required String id,
    required String text,
    required DateTime timestamp,
    String? associatedTransactionId,
  }) {
    return ChatMessage(
      id: id,
      text: text,
      timestamp: timestamp,
      isUserMessage: false,
      associatedTransactionId: associatedTransactionId,
      type: ChatMessageType.system,
    );
  }

  /// Factory constructor for system messages with quick replies
  factory ChatMessage.systemWithQuickReplies({
    required String id,
    required String text,
    required DateTime timestamp,
    required List<String> quickReplies,
    required String quickReplyId,
    String? associatedTransactionId,
  }) {
    return ChatMessage(
      id: id,
      text: text,
      timestamp: timestamp,
      isUserMessage: false,
      associatedTransactionId: associatedTransactionId,
      type: ChatMessageType.systemWithQuickReplies,
      quickReplies: quickReplies,
      quickReplyId: quickReplyId,
    );
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      text: json['text'],
      timestamp: DateTime.parse(json['timestamp']),
      isUserMessage: json['isUserMessage'],
      associatedTransactionId: json['associatedTransactionId'],
      type: json['type'] != null
          ? ChatMessageType.values.byName(json['type'])
          : (json['isUserMessage'] ? ChatMessageType.user : ChatMessageType.system),
      quickReplies: json['quickReplies'] != null
          ? List<String>.from(json['quickReplies'])
          : null,
      quickReplyId: json['quickReplyId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'timestamp': timestamp.toIso8601String(),
      'isUserMessage': isUserMessage,
      'associatedTransactionId': associatedTransactionId,
      'type': type.name,
      'quickReplies': quickReplies,
      'quickReplyId': quickReplyId,
    };
  }
}

class TransactionProvider with ChangeNotifier {
  final StorageService _storageService;
  LearnedAssociationService? _learnedAssociationService;
  List<Transaction> _transactions = [];
  List<Category> _categories = [];
  List<ChatMessage> _messages = [];

  // For pagination
  bool _isLoading = false;
  bool _hasMoreMessages = true;
  final int _messagesPerPage = 20;

  // Initialization state management
  bool _isInitializing = true;
  bool _isInitialized = false;
  String? _initializationError;

  // Default categories
  final List<Category> _defaultCategories = [
    // Expense categories
    Category(id: 'food', name: 'Food', icon: '🍔', colorValue: 0xFFFF9800, type: TransactionType.expense),
    Category(id: 'transport', name: 'Transport', icon: '🚗', colorValue: 0xFF2196F3, type: TransactionType.expense),
    Category(id: 'shopping', name: 'Shopping', icon: '🛍️', colorValue: 0xFFF44336, type: TransactionType.expense),
    Category(id: 'utilities', name: 'Utilities', icon: '💡', colorValue: 0xFF4CAF50, type: TransactionType.expense),
    Category(id: 'entertainment', name: 'Entertainment', icon: '🎬', colorValue: 0xFF9C27B0, type: TransactionType.expense),
    Category(id: 'health', name: 'Health', icon: '🏥', colorValue: 0xFF009688, type: TransactionType.expense),
    Category(id: 'gift_expense', name: 'Gifts', icon: '🎁', colorValue: 0xFFE91E63, type: TransactionType.expense),
    Category(id: 'other', name: 'Other', icon: '❓', colorValue: 0xFF607D8B, type: TransactionType.expense),
    
    // Income categories
    Category(id: 'salary', name: 'Salary', icon: '💰', colorValue: 0xFF4CAF50, type: TransactionType.income),
    Category(id: 'gift', name: 'Gift', icon: '🎁', colorValue: 0xFF9C27B0, type: TransactionType.income),
    Category(id: 'investment', name: 'Investment', icon: '📈', colorValue: 0xFF2196F3, type: TransactionType.income),
    Category(id: 'bonus', name: 'Bonus', icon: '🎉', colorValue: 0xFFFFEB3B, type: TransactionType.income),
    Category(id: 'sales', name: 'Sales', icon: '💳', colorValue: 0xFF3F51B5, type: TransactionType.income),
    Category(id: 'crypto', name: 'Crypto', icon: '💲', colorValue: 0xFFFF5722, type: TransactionType.income),
    Category(id: 'refund', name: 'Refund', icon: '🔄', colorValue: 0xFF795548, type: TransactionType.income),
    Category(id: 'other_income', name: 'Other Income', icon: '💸', colorValue: 0xFF607D8B, type: TransactionType.income),
    
    // Loan category
    Category(id: 'loan', name: 'Loan', icon: '🏦', colorValue: 0xFFFF9800, type: TransactionType.loan),
  ];

  TransactionProvider(this._storageService) {
    // Don't load data synchronously - set initial loading state instead
    // Data will be loaded asynchronously via initialize() method
  }

  /// Initialize the provider asynchronously
  Future<void> initialize() async {
    if (_isInitialized) return;

    _isInitializing = true;
    _initializationError = null;
    notifyListeners();

    try {
      // Load all data asynchronously
      await _loadData();

      // Initialize learned association service
      await _initializeLearnedAssociationService();

      _isInitialized = true;
      _isInitializing = false;
      notifyListeners();
    } catch (e) {
      _initializationError = e.toString();
      _isInitializing = false;
      print('Failed to initialize TransactionProvider: $e');
      notifyListeners();
      rethrow;
    }
  }

  /// Initialize the learned association service
  Future<void> _initializeLearnedAssociationService() async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(_storageService);
    } catch (e) {
      print('Failed to initialize learned association service: $e');
      rethrow;
    }
  }

  /// Wait for learned association service to be initialized (for testing)
  Future<void> waitForLearnedAssociationService() async {
    while (_learnedAssociationService == null) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }

  List<Transaction> get transactions => _transactions;
  List<Category> get categories => _categories;
  List<ChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  bool get hasMoreMessages => _hasMoreMessages;

  // Initialization state getters
  bool get isInitializing => _isInitializing;
  bool get isInitialized => _isInitialized;
  String? get initializationError => _initializationError;

  // Get transactions by type
  List<Transaction> getTransactionsByType(TransactionType type) {
    return _transactions.where((transaction) => transaction.type == type).toList();
  }

  // Get transactions within date range
  List<Transaction> getTransactionsInDateRange(DateTime startDate, DateTime endDate) {
    return _transactions.where((transaction) => 
      transaction.date.isAfter(startDate.subtract(const Duration(days: 1))) && 
      transaction.date.isBefore(endDate.add(const Duration(days: 1)))
    ).toList();
  }

  // Get category by ID
  Category? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  // Add a new transaction
  Future<void> addTransaction(Transaction transaction) async {
    _transactions.add(transaction);
    await _saveTransactions();
    notifyListeners();
  }

  // Add a transaction directly from chat (no confirmation)
  Future<void> addTransactionFromChat(Transaction transaction, String userMessage) async {
    // First add the transaction
    await addTransaction(transaction);
    
    // Then generate response message
    final category = getCategoryById(transaction.categoryId);
    final categoryName = category?.name ?? 'Other';
    
    // Add system response with the transaction details
    final message = ChatMessage(
      id: transaction.id,
      text: '✅ Transaction saved: ${formatCurrency(transaction.amount)} for ${transaction.description}, Category: $categoryName',
      timestamp: DateTime.now(),
      isUserMessage: false,
      associatedTransactionId: transaction.id,
    );
    
    // Make sure to await this so the UI can update accordingly
    await addMessage(message);
  }

  // Update an existing transaction
  Future<void> updateTransaction(Transaction updatedTransaction) async {
    final index = _transactions.indexWhere((t) => t.id == updatedTransaction.id);
    if (index != -1) {
      final originalTransaction = _transactions[index];

      // Check if type or category changed and trigger learning
      if (originalTransaction.description.trim().isNotEmpty &&
          (originalTransaction.type != updatedTransaction.type ||
           originalTransaction.categoryId != updatedTransaction.categoryId)) {

        // Ensure learned association service is initialized before learning
        await waitForLearnedAssociationService();

        try {
          await _learnedAssociationService!.learn(
            originalTransaction.description,
            type: updatedTransaction.type,
            categoryId: updatedTransaction.categoryId,
          );
        } catch (e) {
          print('Failed to learn from transaction edit: $e');
          // Don't prevent the update if learning fails
        }
      }

      _transactions[index] = updatedTransaction;
      await _saveTransactions();
      notifyListeners();
    }
  }

  // Delete a transaction
  Future<void> deleteTransaction(String id) async {
    _transactions.removeWhere((transaction) => transaction.id == id);
    
    // Also remove any associated chat messages
    _messages.removeWhere((message) => message.associatedTransactionId == id);
    
    await _saveTransactions();
    await _saveMessages();
    notifyListeners();
  }

  // Add a new category
  Future<void> addCategory(Category category) async {
    _categories.add(category);
    await _saveCategories();
    notifyListeners();
  }

  // Update an existing category
  Future<void> updateCategory(Category updatedCategory) async {
    final index = _categories.indexWhere((c) => c.id == updatedCategory.id);
    if (index != -1) {
      _categories[index] = updatedCategory;
      await _saveCategories();
      notifyListeners();
    }
  }

  // Delete a category
  Future<void> deleteCategory(String id) async {
    _categories.removeWhere((category) => category.id == id);
    await _saveCategories();
    notifyListeners();
  }

  // Add a new chat message
  Future<void> addMessage(ChatMessage message) async {
    _messages.add(message);
    await _saveMessages();
    notifyListeners();
  }
  
  // Load more messages (for pagination)
  Future<void> loadMoreMessages() async {
    if (_isLoading || !_hasMoreMessages) return;
    
    _isLoading = true;
    notifyListeners();
    
    await Future.delayed(const Duration(milliseconds: 500)); // simulate loading
    
    // Load all messages from storage
    final allMessages = await _getAllMessagesFromStorage();
    if (allMessages.isEmpty) {
      _isLoading = false;
      _hasMoreMessages = false;
      notifyListeners();
      return;
    }
    
    // Sort by timestamp (oldest to newest)
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // Find the oldest message we currently have
    final oldestTimestamp = _messages.isEmpty ? DateTime.now() : _messages.first.timestamp;
    
    // Get messages older than our oldest message
    final olderMessages = allMessages.where((m) => m.timestamp.isBefore(oldestTimestamp)).toList();
    
    if (olderMessages.isEmpty) {
      _hasMoreMessages = false;
    } else {
      // Take up to messagesPerPage older messages
      final messagesToAdd = olderMessages.length > _messagesPerPage 
          ? olderMessages.sublist(olderMessages.length - _messagesPerPage) 
          : olderMessages;
      
      // Insert at the beginning (older messages)
      _messages.insertAll(0, messagesToAdd);
      
      // If we got all remaining messages, there are no more to load
      _hasMoreMessages = olderMessages.length > messagesToAdd.length;
    }
    
    _isLoading = false;
    notifyListeners();
  }

  // Load all data from storage
  Future<void> _loadData() async {
    await _loadTransactions();
    await _loadCategories();
    await _loadInitialMessages();
  }
  
  // Load initial batch of messages
  Future<void> _loadInitialMessages() async {
    final allMessages = await _getAllMessagesFromStorage();
    
    if (allMessages.isEmpty) {
      _hasMoreMessages = false;
      return;
    }
    
    // Sort by timestamp (oldest to newest)
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // Take only the most recent messages
    final totalMessages = allMessages.length;
    final startIndex = totalMessages > _messagesPerPage ? totalMessages - _messagesPerPage : 0;
    _messages = allMessages.sublist(startIndex, totalMessages);
    
    // If we loaded from the beginning, there are no more older messages to load
    _hasMoreMessages = startIndex > 0;
  }
  
  // Get all messages from storage
  Future<List<ChatMessage>> _getAllMessagesFromStorage() async {
    final messagesJson = await _storageService.getStringList('messages');
    if (messagesJson != null && messagesJson.isNotEmpty) {
      return messagesJson
          .map((json) => ChatMessage.fromJson(jsonDecode(json)))
          .toList();
    }
    return [];
  }

  // Load transactions from storage
  Future<void> _loadTransactions() async {
    final transactionsJson = await _storageService.getStringList('transactions');
    if (transactionsJson != null) {
      _transactions = transactionsJson
          .map((json) => Transaction.fromJson(jsonDecode(json)))
          .toList();
    }
  }

  // Save transactions to storage
  Future<void> _saveTransactions() async {
    final transactionsJson = _transactions
        .map((transaction) => jsonEncode(transaction.toJson()))
        .toList();
    await _storageService.setStringList('transactions', transactionsJson);
  }

  // Load categories from storage
  Future<void> _loadCategories() async {
    final categoriesJson = await _storageService.getStringList('categories');
    if (categoriesJson != null && categoriesJson.isNotEmpty) {
      _categories = categoriesJson
          .map((json) => Category.fromJson(jsonDecode(json)))
          .toList();
    } else {
      // Use default categories if none are saved
      _categories = _defaultCategories;
      await _saveCategories();
    }
  }

  // Save categories to storage
  Future<void> _saveCategories() async {
    final categoriesJson = _categories
        .map((category) => jsonEncode(category.toJson()))
        .toList();
    await _storageService.setStringList('categories', categoriesJson);
  }

  // Save messages to storage
  Future<void> _saveMessages() async {
    final messagesJson = _messages
        .map((message) => jsonEncode(message.toJson()))
        .toList();
    await _storageService.setStringList('messages', messagesJson);
  }

  // Get total for a specific transaction type within date range
  double getTotalByTypeInRange(TransactionType type, DateTime startDate, DateTime endDate) {
    return getTransactionsInDateRange(startDate, endDate)
        .where((t) => t.type == type)
        .fold(0, (sum, transaction) => sum + transaction.amount);
  }

  // Get spending by category within date range
  Map<String, double> getSpendingByCategory(DateTime startDate, DateTime endDate) {
    final Map<String, double> result = {};
    
    final filteredTransactions = getTransactionsInDateRange(startDate, endDate)
        .where((t) => t.type == TransactionType.expense);
    
    for (var transaction in filteredTransactions) {
      if (result.containsKey(transaction.categoryId)) {
        result[transaction.categoryId] = result[transaction.categoryId]! + transaction.amount;
      } else {
        result[transaction.categoryId] = transaction.amount;
      }
    }
    
    return result;
  }

  // Format currency amount
  String formatCurrency(double amount, [String? currencyCode]) {
    if (currencyCode != null) {
      return CurrencyUtils.formatCurrencyAmount(amount, currencyCode);
    }
    return NumberFormat.currency(symbol: "\$", decimalDigits: 2).format(amount);
  }
  
  // Clear all data
  Future<void> clearAllData() async {
    // Clear all transactions
    _transactions.clear();
    await _saveTransactions();
    
    // Clear all messages
    _messages.clear();
    await _saveMessages();
    
    // Reset categories to defaults
    _categories = _defaultCategories;
    await _saveCategories();
    
    notifyListeners();
  }
}
</file>

<file path="navigation/app_navigation.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../screens/chat_screen.dart';
import '../screens/statistics_screen.dart';
import '../screens/settings_screen.dart';
import '../services/startup_service.dart';
import '../widgets/startup_loading_widget.dart';

class AppNavigation extends StatefulWidget {
  const AppNavigation({super.key});

  @override
  State<AppNavigation> createState() => _AppNavigationState();
}

class _AppNavigationState extends State<AppNavigation> {
  int _currentIndex = 0;
  bool _servicesInitialized = false;

  // Create screens only once to preserve their state
  final List<Widget> _screens = const [
    ChatScreen(),
    StatisticsScreen(),
    SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    final startupService = Provider.of<StartupService>(context, listen: false);

    // Start service initialization
    await startupService.initializeAllServices();

    if (mounted) {
      setState(() {
        _servicesInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading screen while services are initializing
    if (!_servicesInitialized) {
      return const StartupLoadingWidget();
    }

    // Get the color scheme from the current theme
    final colorScheme = Theme.of(context).colorScheme;
    
    // Use the color scheme to define the navigation bar colors
    final navigationBarTheme = NavigationBarThemeData(
      indicatorColor: colorScheme.secondaryContainer,
      labelTextStyle: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return TextStyle(color: colorScheme.onSecondaryContainer, fontSize: 12);
        }
        return TextStyle(color: colorScheme.onSurfaceVariant, fontSize: 12);
      }),
    );
    
    return Scaffold(
      // Use IndexedStack instead of directly showing the screen
      // This keeps all screens in memory and preserves their state
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          navigationBarTheme: navigationBarTheme,
        ),
        child: NavigationBar(
          selectedIndex: _currentIndex,
          onDestinationSelected: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
          animationDuration: const Duration(milliseconds: 500),
          destinations: const [
            NavigationDestination(
              icon: Icon(Icons.chat_bubble_outline),
              selectedIcon: Icon(Icons.chat_bubble),
              label: 'Chat',
            ),
            NavigationDestination(
              icon: Icon(Icons.bar_chart_outlined),
              selectedIcon: Icon(Icons.bar_chart),
              label: 'Statistics',
            ),
            NavigationDestination(
              icon: Icon(Icons.settings_outlined),
              selectedIcon: Icon(Icons.settings),
              label: 'Settings',
            ),
          ],
        ),
      ),
    );
  }
}
</file>

<file path="screens/categories_screen.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../models/transaction_model.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final Uuid _uuid = Uuid();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Categories'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'EXPENSE'),
            Tab(text: 'INCOME'),
            Tab(text: 'LOAN'),
          ],
          indicatorColor: theme.colorScheme.primary,
          labelColor: theme.brightness == Brightness.light 
              ? theme.colorScheme.primary 
              : Colors.white,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoryList(TransactionType.expense),
          _buildCategoryList(TransactionType.income),
          _buildCategoryList(TransactionType.loan),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCategoryDialog(),
        child: const Icon(Icons.add),
        tooltip: 'Add Category',
      ),
    );
  }

  Widget _buildCategoryList(TransactionType type) {
    return Consumer<TransactionProvider>(
      builder: (context, provider, child) {
        final categories = provider.categories
            .where((category) => category.type == type)
            .toList();
        
        if (categories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.category_outlined,
                  size: 64,
                  color: Colors.grey.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No ${type.name} categories yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCategoryDialog(type: type),
                  icon: const Icon(Icons.add),
                  label: Text('Add ${type.name} category'),
                ),
              ],
            ),
          );
        }
        
        return AnimatedList(
          key: GlobalKey<AnimatedListState>(),
          initialItemCount: categories.length,
          padding: const EdgeInsets.all(16),
          itemBuilder: (context, index, animation) {
            final category = categories[index];
            return SlideTransition(
              position: Tween<Offset>(begin: const Offset(1, 0), end: Offset.zero).animate(
                CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOutQuint,
                ),
              ),
              child: FadeTransition(
                opacity: animation,
                child: Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(category.colorValue).withOpacity(0.2),
                      child: Text(
                        category.icon,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                    title: Text(category.name),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _showCategoryDialog(category: category),
                          tooltip: 'Edit',
                        ),
                        // Only show delete button if it's not a default category
                        if (!_isDefaultCategory(category.id))
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteCategory(category),
                            tooltip: 'Delete',
                          ),
                      ],
                    ),
                    onTap: () => _showCategoryDialog(category: category),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showCategoryDialog({Category? category, TransactionType? type}) {
    // If category is provided, we're editing, otherwise we're creating a new one
    final isEditing = category != null;
    
    // Initial values
    final initialType = category?.type ?? type ?? TransactionType.expense;
    final initialName = category?.name ?? '';
    final initialIcon = category?.icon ?? '\ud83d\udcb0';
    final initialColor = category != null ? Color(category.colorValue) : Colors.blue;
    
    // Form controllers
    final nameController = TextEditingController(text: initialName);
    
    // State management
    TransactionType selectedType = initialType;
    String selectedIcon = initialIcon;
    Color selectedColor = initialColor;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(isEditing ? 'Edit Category' : 'New Category'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Type selector
                  const Text('Type', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  SegmentedButton<TransactionType>(
                    segments: const [
                      ButtonSegment(
                        value: TransactionType.expense, 
                        label: Text('Expense'),
                        icon: Icon(Icons.arrow_upward),
                      ),
                      ButtonSegment(
                        value: TransactionType.income, 
                        label: Text('Income'),
                        icon: Icon(Icons.arrow_downward),
                      ),
                      ButtonSegment(
                        value: TransactionType.loan,
                        label: Text('Loan'),
                        icon: Icon(Icons.compare_arrows),
                      ),
                    ],
                    selected: {selectedType},
                    onSelectionChanged: (Set<TransactionType> selected) {
                      setState(() {
                        selectedType = selected.first;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Name field
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(),
                    ),
                    textCapitalization: TextCapitalization.words,
                  ),
                  const SizedBox(height: 16),
                  
                  // Icon selector
                  const Text('Icon', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildIconSelector(selectedIcon, (icon) {
                    setState(() {
                      selectedIcon = icon;
                    });
                  }),
                  const SizedBox(height: 16),
                  
                  // Color selector
                  const Text('Color', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildColorSelector(selectedColor, (color) {
                    setState(() {
                      selectedColor = color;
                    });
                  }),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  final name = nameController.text.trim();
                  if (name.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter a category name')),
                    );
                    return;
                  }
                  
                  if (isEditing) {
                    _updateCategory(
                      category.copyWith(
                        name: name,
                        icon: selectedIcon,
                        colorValue: selectedColor.value,
                        type: selectedType,
                      ),
                    );
                  } else {
                    _addCategory(
                      id: _generateCategoryId(name),
                      name: name,
                      icon: selectedIcon,
                      colorValue: selectedColor.value,
                      type: selectedType,
                    );
                  }
                  
                  Navigator.of(context).pop();
                },
                child: Text(isEditing ? 'UPDATE' : 'CREATE'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildIconSelector(String selectedIcon, Function(String) onIconSelected) {
    final icons = [
      '\ud83d\udcb0', '\ud83d\udcb5', '\ud83d\udcb3', '\ud83c\udf54', '\ud83d\ude97', 
      '\ud83d\udecd\ufe0f', '\ud83d\udca1', '\ud83c\udfac', '\ud83c\udfe5', '\ud83c\udf81', 
      '\ud83c\udfe6', '\ud83d\udcaa', '\ud83d\udcbb', '\ud83d\udcf1', '\ud83d\udc55', 
      '\ud83d\udc57', '\ud83d\udc5f', '\ud83c\udfd0', '\ud83c\udfcb\ufe0f', '\ud83d\udeba',
      '\ud83d\udcc8', '\ud83c\udf89', '\ud83d\udcb2', '\ud83d\udd04', '\ud83d\udcb8',
    ];
    
    return Container(
      height: 120,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GridView.builder(
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
        ),
        itemCount: icons.length,
        itemBuilder: (context, index) {
          final icon = icons[index];
          final isSelected = icon == selectedIcon;
          
          return Material(
            color: isSelected ? Theme.of(context).colorScheme.primary.withOpacity(0.2) : Colors.transparent,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: () => onIconSelected(icon),
              child: Center(
                child: Text(
                  icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildColorSelector(Color selectedColor, Function(Color) onColorSelected) {
    final colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
    ];
    
    return Container(
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.all(8),
        itemCount: colors.length,
        itemBuilder: (context, index) {
          final color = colors[index];
          final isSelected = color.value == selectedColor.value;
          
          return GestureDetector(
            onTap: () => onColorSelected(color),
            child: Container(
              width: 34,
              height: 34,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: isSelected
                    ? Border.all(color: Colors.white, width: 2)
                    : null,
                boxShadow: isSelected
                    ? [BoxShadow(color: color.withOpacity(0.5), blurRadius: 8)]
                    : null,
              ),
              child: isSelected 
                  ? const Icon(Icons.check, color: Colors.white, size: 18) 
                  : null,
            ),
          );
        },
      ),
    );
  }

  void _addCategory({
    required String id,
    required String name,
    required String icon,
    required int colorValue,
    required TransactionType type,
  }) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    final newCategory = Category(
      id: id,
      name: name,
      icon: icon,
      colorValue: colorValue,
      type: type,
    );
    
    provider.addCategory(newCategory);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Category "$name" created')),
    );
  }

  void _updateCategory(Category updatedCategory) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    provider.updateCategory(updatedCategory);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Category "${updatedCategory.name}" updated')),
    );
  }

  void _deleteCategory(Category category) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text(
          'Are you sure you want to delete "${category.name}"? ' 
          'Any transactions using this category will be affected.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              provider.deleteCategory(category.id);
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Category "${category.name}" deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  String _generateCategoryId(String name) {
    // Generate a unique ID based on the name and a random UUID
    return '${name.toLowerCase().replaceAll(' ', '_')}_${_uuid.v4().substring(0, 8)}';
  }

  bool _isDefaultCategory(String id) {
    // Default category IDs list to prevent deletion
    final defaultCategoryIds = [
      'food', 'transport', 'shopping', 'utilities', 'entertainment', 'health',
      'salary', 'gift', 'investment', 'bonus', 'sales', 'crypto', 'refund', 'other_income',
      'loan', 'gift_expense',
    ];
    
    return defaultCategoryIds.contains(id);
  }
}

// Helper to create a copy of Category with updated fields
extension CategoryExtension on Category {
  Category copyWith({
    String? id,
    String? name,
    String? icon,
    int? colorValue,
    TransactionType? type,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      colorValue: colorValue ?? this.colorValue,
      type: type ?? this.type,
    );
  }
}
</file>

<file path="screens/chat_screen.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../models/transaction_model.dart';
import '../models/parse_result.dart';
import '../services/parser/mlkit_parser_service.dart';
import '../services/parser/learned_association_service.dart';
import '../services/storage_service.dart';
import '../services/startup_service.dart';
import '../widgets/transaction_message.dart';
import '../widgets/transaction_edit_dialog.dart';
import '../widgets/category_picker_dialog.dart';
import '../widgets/quick_reply_widget.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  MlKitParserService? _parserService;
  LearnedAssociationService? _learnedAssociationService;
  final Uuid _uuid = Uuid();
  late AnimationController _animationController;
  bool _initialLoading = true;

  // State management for pending type selection
  ParseResult? _pendingTypeSelection;
  String? _pendingOriginalText;

  // State management for pending amount confirmation
  ParseResult? _pendingAmountConfirmation;

  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Initialize ML Kit parser service
    _initializeParserService();

    // Add welcome message if this is the first time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      
      // Add listener to scroll to bottom when messages change
      provider.addListener(_onProviderChanged);
      
      // Hide the initial loading indicator after a short delay
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _initialLoading = false;
          });
          
          // Scroll to bottom after loading is done
          _scrollToBottom();
        }
      });
      
      if (provider.messages.isEmpty) {
        _addSystemMessage('👋 Welcome to Money Lover Chat! Tell me about your financial transactions and I\'ll help you keep track of them.\n\nTry saying something like "Spent \$25 on dinner" or "Got \$1500 salary today".');
      }
      
      // Setup scroll controller to load more messages when scrolling to top
      _scrollController.addListener(_scrollListener);
    });
  }

  void _initializeParserService() async {
    // Use services from the StartupService instead of creating new instances
    final startupService = Provider.of<StartupService>(context, listen: false);

    // Wait for services to be available or use what's currently available
    _parserService = startupService.mlKitService;

    // If ML Kit service is not ready yet, show appropriate message
    if (_parserService == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Services are initializing... Please wait.'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _animationController.dispose();
    
    // Remove provider listener
    Provider.of<TransactionProvider>(context, listen: false).removeListener(_onProviderChanged);
    
    super.dispose();
  }
  
  void _scrollListener() {
    if (_scrollController.position.pixels <= _scrollController.position.minScrollExtent + 50) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      
      // Save current position and content size
      final currentPosition = _scrollController.position.pixels;
      final currentContentSize = _scrollController.position.maxScrollExtent;
      
      provider.loadMoreMessages().then((_) {
        // Wait for layout to update with new content
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            // Calculate new position to maintain relative scroll position
            final newContentSize = _scrollController.position.maxScrollExtent;
            final newPosition = newContentSize - currentContentSize + currentPosition;
            
            if (newPosition > 0) {
              _scrollController.jumpTo(newPosition);
            }
          }
        });
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Add user message
    _addUserMessage(text);

    // Clear input field
    _messageController.clear();

    // Parse transaction
    if (_parserService != null) {
      try {
        final parseResult = await _parserService!.parseTransaction(text);

        // Handle different parse statuses
        switch (parseResult.status) {
          case ParseStatus.success:
            // Automatically save the transaction
            await provider.addTransactionFromChat(parseResult.transaction, text);
            _scrollToBottom();
            break;

          case ParseStatus.needsCategory:
            // Show category picker dialog
            await _handleCategorySelection(parseResult, text);
            break;

          case ParseStatus.needsType:
            // Show quick reply for transaction type selection
            await _handleTypeSelection(parseResult, text);
            break;

          case ParseStatus.needsAmountConfirmation:
            // Show amount confirmation with quick replies
            print('DEBUG: Amount confirmation triggered for text: "$text"');
            print('DEBUG: candidateAmounts: ${parseResult.candidateAmounts}');
            print('DEBUG: candidateTexts: ${parseResult.candidateTexts}');
            await _handleAmountConfirmation(parseResult, text);
            break;

          case ParseStatus.failed:
            // Parsing failed - show error message
            _addSystemMessage(
              parseResult.error ??
              "I couldn't detect a transaction in your message. Try using formats like "
              "\"spent \$50 on groceries\" or \"received \$1000 salary\".",
            );
            break;
        }
      } catch (e) {
        _addSystemMessage(
          "Sorry, there was an error processing your message. Please try again.",
        );
      }
    } else {
      // Parser service not initialized
      _addSystemMessage(
        "Parser service is not ready yet. Please try again in a moment.",
      );
    }
  }

  Future<void> _handleTypeSelection(ParseResult parseResult, String originalText) async {
    // Store pending transaction for type selection
    _pendingTypeSelection = parseResult;
    _pendingOriginalText = originalText;

    // Create system message with quick replies for transaction type
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: 'I see ${_formatCurrency(parseResult.transaction.amount, parseResult.transaction.currencyCode)} for "${parseResult.transaction.description}". What type of transaction is this?',
      timestamp: DateTime.now(),
      quickReplies: const ['Expense', 'Income', 'Cancel'],
      quickReplyId: quickReplyId,
    );

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.addMessage(message);
    _scrollToBottom();
  }

  Future<void> _handleAmountConfirmation(ParseResult parseResult, String originalText) async {
    print('DEBUG: _handleAmountConfirmation called');
    print('DEBUG: parseResult.candidateTexts: ${parseResult.candidateTexts}');
    print('DEBUG: parseResult.candidateAmounts: ${parseResult.candidateAmounts}');

    // Null safety check for candidateTexts
    final candidateTexts = parseResult.candidateTexts;
    if (candidateTexts == null || candidateTexts.isEmpty) {
      print('ERROR: candidateTexts is null or empty');
      _addSystemMessage('Sorry, I encountered an error processing the amounts. Please try again.');
      return;
    }

    // Store pending transaction for amount confirmation
    _pendingAmountConfirmation = parseResult;
    _pendingOriginalText = originalText;

    print('DEBUG: Creating quick replies with candidates: $candidateTexts');

    // Create system message with quick replies for amount selection
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: 'I found multiple possible amounts in your message. Which amount did you mean?',
      timestamp: DateTime.now(),
      quickReplies: [...candidateTexts, 'Cancel'],
      quickReplyId: quickReplyId,
    );

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.addMessage(message);
    _scrollToBottom();

    print('DEBUG: Amount confirmation UI displayed successfully');
  }

  Future<void> _handleCategorySelection(ParseResult parseResult, String originalText) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Show category picker dialog
    final selectedCategoryId = await showCategoryPickerDialog(
      context: context,
      transactionType: parseResult.transaction.type,
      initialCategoryId: parseResult.transaction.categoryId,
    );

    if (selectedCategoryId != null) {
      // Update transaction with selected category
      final updatedTransaction = parseResult.transaction.copyWith(
        categoryId: selectedCategoryId,
      );

      // Save the learning association (both type and category if available)
      if (_learnedAssociationService != null) {
        await _learnedAssociationService!.learn(
          originalText,
          type: updatedTransaction.type,
          categoryId: selectedCategoryId,
        );
      }

      // Save the transaction
      await provider.addTransactionFromChat(updatedTransaction, originalText);

      // Add learning confirmation message
      final category = provider.getCategoryById(selectedCategoryId);
      if (category != null) {
        // Extract key words from the original text for learning message
        final keywords = _extractKeywords(originalText);
        if (keywords.isNotEmpty) {
          _addSystemMessage(
            '💡 I\'ll remember to categorize "${keywords}" as ${category.name} for you next time.',
          );
        }
      }

      _scrollToBottom();
    } else {
      // User cancelled - save with original category
      await provider.addTransactionFromChat(parseResult.transaction, originalText);
      _scrollToBottom();
    }
  }

  void _addUserMessage(String text) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final message = ChatMessage.user(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
    );
    provider.addMessage(message);

    // Always scroll to bottom when a new message is added
    _scrollToBottom();
  }

  void _addSystemMessage(String text, {String? transactionId}) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final message = ChatMessage.system(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
      associatedTransactionId: transactionId,
    );
    provider.addMessage(message);

    // Always scroll to bottom when a new message is added
    _scrollToBottom();
  }

  // Handle quick reply selection
  Future<void> _onQuickReplySelected(String quickReplyId, String selectedOption) async {
    print('DEBUG: _onQuickReplySelected called with quickReplyId: "$quickReplyId", selectedOption: "$selectedOption"');
    print('DEBUG: _pendingTypeSelection: ${_pendingTypeSelection != null}');
    print('DEBUG: _pendingAmountConfirmation: ${_pendingAmountConfirmation != null}');

    if (_pendingTypeSelection != null && _pendingOriginalText != null) {
      print('DEBUG: Routing to type selection response');
      await _handleTypeSelectionResponse(selectedOption);
    } else if (_pendingAmountConfirmation != null && _pendingOriginalText != null) {
      print('DEBUG: Routing to amount confirmation response');
      await _handleAmountConfirmationResponse(selectedOption);
    } else {
      print('ERROR: No pending selection found for quick reply');
    }
  }

  // Handle transaction type selection response
  Future<void> _handleTypeSelectionResponse(String selectedType) async {
    if (_pendingTypeSelection == null || _pendingOriginalText == null) return;

    if (selectedType == 'Cancel') {
      // User cancelled
      _addSystemMessage('Transaction cancelled.');
      _pendingTypeSelection = null;
      _pendingOriginalText = null;
      return;
    }

    // Convert selected type to TransactionType enum
    TransactionType? transactionType;
    switch (selectedType) {
      case 'Expense':
        transactionType = TransactionType.expense;
        break;
      case 'Income':
        transactionType = TransactionType.income;
        break;
      default:
        transactionType = TransactionType.expense;
    }

    // Learn the type association
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(_pendingOriginalText!, type: transactionType);
    }

    // Update the pending transaction with the selected type
    final updatedTransaction = _pendingTypeSelection!.transaction.copyWith(
      type: transactionType,
    );

    // Now proceed to category detection
    // For now, we'll assume category selection is needed and let the user choose
    final parseResult = ParseResult.needsCategory(updatedTransaction);
    await _handleCategorySelection(parseResult, _pendingOriginalText!);

    // Clear pending state
    _pendingTypeSelection = null;
    _pendingOriginalText = null;
  }

  // Handle amount confirmation response
  Future<void> _handleAmountConfirmationResponse(String selectedOption) async {
    print('DEBUG: _handleAmountConfirmationResponse called with option: "$selectedOption"');

    if (_pendingAmountConfirmation == null || _pendingOriginalText == null) {
      print('ERROR: No pending amount confirmation found');
      return;
    }

    if (selectedOption == 'Cancel') {
      // User cancelled
      print('DEBUG: User cancelled amount confirmation');
      _addSystemMessage('Transaction cancelled.');
      _pendingAmountConfirmation = null;
      _pendingOriginalText = null;
      return;
    }

    // Find the selected amount from the candidate texts with null safety
    final candidateTexts = _pendingAmountConfirmation!.candidateTexts;
    final candidateAmounts = _pendingAmountConfirmation!.candidateAmounts;

    if (candidateTexts == null || candidateAmounts == null) {
      print('ERROR: candidateTexts or candidateAmounts is null');
      _addSystemMessage('Sorry, I encountered an error processing your selection. Please try again.');
      return;
    }

    print('DEBUG: Looking for "$selectedOption" in candidates: $candidateTexts');

    final selectedIndex = candidateTexts.indexOf(selectedOption);
    if (selectedIndex == -1) {
      print('ERROR: Selected option "$selectedOption" not found in candidates');
      _addSystemMessage('Invalid selection. Please try again.');
      return;
    }

    print('DEBUG: Found selected option at index $selectedIndex');
    final confirmedAmount = candidateAmounts[selectedIndex];
    print('DEBUG: User selected amount: $confirmedAmount');

    // Complete the transaction with the confirmed amount using the parser service
    if (_parserService != null) {
      try {
        final parseResult = await _parserService!.completeTransaction(_pendingOriginalText!, confirmedAmount);

        // Handle the result (might need category selection)
        switch (parseResult.status) {
          case ParseStatus.success:
            final provider = Provider.of<TransactionProvider>(context, listen: false);
            await provider.addTransactionFromChat(parseResult.transaction, _pendingOriginalText!);
            _addSystemMessage('✅ Transaction saved with amount ${_formatCurrency(confirmedAmount, parseResult.transaction.currencyCode)}');
            break;
          case ParseStatus.needsCategory:
            await _handleCategorySelection(parseResult, _pendingOriginalText!);
            break;
          default:
            _addSystemMessage('Error completing transaction. Please try again.');
        }
      } catch (e) {
        _addSystemMessage('Error processing transaction: $e');
      }
    }

    // Clear pending state
    _pendingAmountConfirmation = null;
    _pendingOriginalText = null;
  }

  // Helper method to format currency
  String _formatCurrency(double amount, String currencyCode) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    return provider.formatCurrency(amount, currencyCode);
  }

  // Helper method to extract keywords from text for learning messages
  String _extractKeywords(String text) {
    // Remove common words and extract meaningful keywords
    final words = text.toLowerCase().split(RegExp(r'\s+'));
    final stopWords = {'for', 'on', 'at', 'in', 'to', 'from', 'with', 'the', 'a', 'an', 'and', 'or', 'but'};
    final keywords = words.where((word) =>
      word.length > 2 &&
      !stopWords.contains(word) &&
      !RegExp(r'^\d+$').hasMatch(word) && // Not just numbers
      !RegExp(r'^[\$€£¥₹₽₩₱₫฿₺₪]+$').hasMatch(word) // Not just currency symbols
    ).take(3).join(' ');

    return keywords;
  }

  Future<void> _showEditDialog(Transaction transaction) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    final result = await showDialog<Transaction>(
      context: context,
      builder: (context) => TransactionEditDialog(transaction: transaction),
    );
    
    if (result != null) {
      // Update the transaction
      await provider.updateTransaction(result);
      
      // Show success message
      final category = provider.getCategoryById(result.categoryId);
      final categoryName = category?.name ?? 'Other';
      
      _addSystemMessage(
        '✅ Transaction updated: ${provider.formatCurrency(result.amount)} for ${result.description}, Category: $categoryName',
        transactionId: result.id,
      );
    }
  }

  Future<void> _showDeleteConfirmation(String transactionId) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final transaction = provider.transactions.firstWhere((t) => t.id == transactionId);
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text(
          'Are you sure you want to delete this transaction: ${provider.formatCurrency(transaction.amount)} for ${transaction.description}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await provider.deleteTransaction(transactionId);
      
      _addSystemMessage(
        '🗑️ Transaction deleted successfully.',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);

    final theme = Theme.of(context);
    final startupService = Provider.of<StartupService>(context);
    final provider = startupService.transactionProvider;

    // Handle case where provider is not yet available
    if (provider == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Row(
            children: [
              Icon(Icons.chat_bubble),
              SizedBox(width: 8),
              Text('Money Lover Chat'),
            ],
          ),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Initializing services...'),
            ],
          ),
        ),
      );
    }

    final messages = provider.messages;
    final isLoading = provider.isLoading;

    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.chat_bubble),
            SizedBox(width: 8),
            Text('Money Lover Chat'),
          ],
        ),
        scrolledUnderElevation: 3,
        shadowColor: theme.colorScheme.shadow.withOpacity(0.1),
      ),
      body: Column(
        children: [
          // Loading indicator for pagination
          if (isLoading && !_initialLoading)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              color: theme.colorScheme.primaryContainer.withOpacity(0.3),
              child: const Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 16, 
                      height: 16, 
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 8),
                    Text('Loading older messages...'),
                  ],
                ),
              ),
            ),
            
          // Messages list
          Expanded(
            child: _initialLoading
                ? _buildInitialLoadingState(theme)
                : (messages.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          final isLastMessage = index == messages.length - 1;
                          
                          // For animation of the last message
                          if (isLastMessage) {
                            _animationController.forward(from: 0.0);
                          }
                          
                          return SlideTransition(
                            position: Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
                              CurvedAnimation(
                                parent: _animationController,
                                curve: Curves.easeOut,
                              ),
                            ),
                            child: FadeTransition(
                              opacity: _animationController,
                              child: _buildMessageBubble(message, theme),
                            ),
                          );
                        },
                      )
                ),
          ),
          
          // Message input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 3,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'Type a transaction...',
                        prefixIcon: const Icon(Icons.text_fields),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => _messageController.clear(),
                          tooltip: 'Clear',
                        ),
                      ),
                      minLines: 1,
                      maxLines: 3,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  FloatingActionButton(
                    onPressed: _sendMessage,
                    mini: true,
                    elevation: 0,
                    child: const Icon(Icons.send),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Start a conversation',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Try saying something like "Spent \$25 on dinner" or "Got \$1500 salary today"',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: 4,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Loading messages...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please wait while we fetch your conversation history',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, ThemeData theme) {
    final isUserMessage = message.type == ChatMessageType.user;
    final hasQuickReplies = message.type == ChatMessageType.systemWithQuickReplies;
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Check if this message is associated with a transaction
    Transaction? associatedTransaction;
    if (message.associatedTransactionId != null) {
      try {
        associatedTransaction = provider.transactions.firstWhere(
          (t) => t.id == message.associatedTransactionId,
        );
      } catch (e) {
        // Transaction might have been deleted
        associatedTransaction = null;
      }
    }

    // Get the category if there's an associated transaction
    Category? category;
    if (associatedTransaction != null) {
      category = provider.getCategoryById(associatedTransaction.categoryId);
    }
    
    return Align(
      alignment: isUserMessage ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isUserMessage 
                ? theme.brightness == Brightness.light 
                    ? theme.colorScheme.primary 
                    : theme.colorScheme.primary.withOpacity(0.8)
                : theme.brightness == Brightness.light 
                    ? Colors.grey.shade200 
                    : Colors.grey.shade800,
            borderRadius: BorderRadius.circular(18).copyWith(
              bottomRight: isUserMessage ? const Radius.circular(0) : null,
              bottomLeft: !isUserMessage ? const Radius.circular(0) : null,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Message text
              Text(
                message.text,
                style: TextStyle(
                  color: isUserMessage 
                      ? Colors.white 
                      : theme.brightness == Brightness.light 
                          ? Colors.black87 
                          : Colors.white,
                  fontSize: 16,
                ),
              ),
              
              // Transaction message if available
              if (associatedTransaction != null && category != null)
                TransactionMessage(
                  transaction: associatedTransaction,
                  category: category,
                  onEdit: _showEditDialog,
                  onDelete: _showDeleteConfirmation,
                ),

              // Quick replies if available
              if (hasQuickReplies && message.quickReplies != null && message.quickReplies!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: QuickReplyWidget(
                    replyOptions: message.quickReplies!,
                    onReplySelected: (selectedOption) {
                      _onQuickReplySelected(message.quickReplyId ?? '', selectedOption);
                    },
                  ),
                ),

              // Timestamp
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  _formatTime(message.timestamp),
                  style: TextStyle(
                    fontSize: 10,
                    color: isUserMessage
                        ? Colors.white.withOpacity(0.7)
                        : Colors.grey,
                  ),
                  textAlign: isUserMessage ? TextAlign.right : TextAlign.left,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    final hours = dateTime.hour.toString().padLeft(2, '0');
    final minutes = dateTime.minute.toString().padLeft(2, '0');
    final time = '$hours:$minutes';
    
    if (date == today) {
      return 'Today, $time';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return 'Yesterday, $time';
    } else {
      final month = dateTime.month.toString().padLeft(2, '0');
      final day = dateTime.day.toString().padLeft(2, '0');
      return '$day/$month, $time';
    }
  }

  // Listen to transaction provider changes to scroll to bottom when messages are added
  void _onProviderChanged() {
    // Check if we need to scroll (only if we're near the bottom already)
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      
      // If we're near the bottom (within 200 pixels), scroll all the way down
      if (maxScroll - currentScroll < 200) {
        _scrollToBottom();
      }
    }
  }
}
</file>

<file path="screens/settings_screen.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/transaction_model.dart';
import '../services/storage_service.dart';
import '../utils/currency_utils.dart';
import '../theme.dart';
import 'categories_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  String _currencyCode = "USD";
  late StorageService _storageService;
  
  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeStorageService();
  }

  Future<void> _initializeStorageService() async {
    _storageService = StorageService();
    await _storageService.init();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    final currencyCode = await _storageService.getDefaultCurrency();
    setState(() {
      _currencyCode = currencyCode;
    });
  }

  Future<void> _setCurrency(String currencyCode) async {
    await _storageService.saveDefaultCurrency(currencyCode);
    setState(() {
      _currencyCode = currencyCode;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your transactions and settings. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    ) ?? false;

    if (confirmed) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      // Reset the provider
      if (mounted) {
        final provider = Provider.of<TransactionProvider>(context, listen: false);
        // This will reload default categories but clear all transactions
        await provider.clearAllData();
        
        // Show confirmation
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('All data has been cleared')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);
    
    final theme = Theme.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.settings),
            SizedBox(width: 8),
            Text('Settings'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _animation,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Appearance Section
            _buildSectionHeader('Appearance', Icons.palette),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Theme',
              description: 'Switch between light and dark mode',
              icon: Icons.brightness_6,
              trailing: Switch(
                value: themeProvider.isDarkMode,
                onChanged: (value) {
                  themeProvider.toggleTheme();
                },
                activeColor: theme.colorScheme.primary,
              ),
            ),

            const SizedBox(height: 24),
            
            // Preferences Section
            _buildSectionHeader('Preferences', Icons.tune),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Currency',
              description: 'Select your preferred currency',
              icon: Icons.attach_money,
              onTap: () => _showCurrencyPicker(),
              trailing: Text(
                _getCurrencyDisplayName(_currencyCode),
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Categories',
              description: 'Manage transaction categories',
              icon: Icons.category,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CategoriesScreen(),
                ),
              ),
            ),

            const SizedBox(height: 24),
            
            // Data Management Section
            _buildSectionHeader('Data Management', Icons.storage),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Clear All Data',
              description: 'Delete all transactions and settings',
              icon: Icons.delete_forever,
              iconColor: Colors.red,
              onTap: _clearAllData,
            ),

            const SizedBox(height: 24),
            
            // About Section
            _buildSectionHeader('About', Icons.info_outline),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'App Version',
              description: 'Money Lover Chat v1.0.0',
              icon: Icons.new_releases,
            ),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Privacy Policy',
              description: 'Read our privacy policy',
              icon: Icons.privacy_tip,
              onTap: () {
                // Show privacy policy dialog
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Privacy Policy'),
                    content: const SingleChildScrollView(
                      child: Text(
                        'This app processes and stores all data locally on your device. '
                        'No personal data is sent to external servers unless you explicitly '
                        'choose to use the AI parsing feature, which would send your message '
                        'text to our servers for processing.\n\n'
                        'We do not collect any user analytics or tracking information.'
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('CLOSE'),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Terms of Service',
              description: 'Read our terms of service',
              icon: Icons.gavel,
              onTap: () {
                // Show terms dialog
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Terms of Service'),
                    content: const SingleChildScrollView(
                      child: Text(
                        'By using this app, you agree to the following terms:\n\n'
                        '1. The app is provided "as is" without warranty of any kind.\n'
                        '2. Your data is stored locally on your device.\n'
                        '3. You are responsible for managing your own financial data.\n'
                        '4. We reserve the right to modify the app features at any time.'
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('CLOSE'),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            const SizedBox(height: 32),
            
            // Footer
            Center(
              child: Text(
                'Made with ❤️ for personal finance',
                style: TextStyle(
                  fontSize: 12,
                  color: theme.brightness == Brightness.light 
                      ? Colors.black54 
                      : Colors.grey.shade300,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          title.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
            color: theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String description,
    required IconData icon,
    Color? iconColor,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: theme.colorScheme.surface,
      margin: EdgeInsets.zero,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: (iconColor ?? theme.colorScheme.primary).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.brightness == Brightness.light 
                            ? Colors.black54 
                            : Colors.grey.shade400,
                      ),
                    ),
                  ],
                ),
              ),
              if (trailing != null) trailing,
              if (onTap != null && trailing == null)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: theme.brightness == Brightness.light 
                      ? Colors.black38 
                      : Colors.grey.shade400,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCurrencyPicker() {
    final currencies = CurrencyUtils.supportedCurrencies;
    
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 16),
            Center(
              child: Container(
                width: 50,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Select Currency',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: currencies.length,
                itemBuilder: (context, index) {
                  final currency = currencies[index];
                  final isSelected = currency['code'] == _currencyCode;
                  
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.1) 
                        : Colors.transparent,
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            currency['symbol']!,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                      title: Text('${currency['code']} (${currency['name']})'),
                      trailing: isSelected 
                          ? Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        _setCurrency(currency['code']!);
                        Navigator.of(context).pop();
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  String _getCurrencyDisplayName(String currencyCode) {
    final symbol = CurrencyUtils.getCurrencySymbol(currencyCode);
    final name = CurrencyUtils.getCurrencyName(currencyCode);
    return '$symbol ($name)';
  }
}
</file>

<file path="screens/statistics_screen.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../models/transaction_model.dart';
import '../utils/currency_utils.dart';
import '../widgets/transaction_edit_dialog.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedPeriod = 'month'; // 'week', 'month', 'year', 'custom'
  bool _showTransactionList = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setPeriod(String period) {
    setState(() {
      _selectedPeriod = period;
      final now = DateTime.now();
      
      switch (period) {
        case 'week':
          _startDate = now.subtract(const Duration(days: 7));
          _endDate = now;
          break;
        case 'month':
          _startDate = DateTime(now.year, now.month - 1, now.day);
          _endDate = now;
          break;
        case 'year':
          _startDate = DateTime(now.year - 1, now.month, now.day);
          _endDate = now;
          break;
        case 'custom':
          // Don't change dates, will be set by date picker
          break;
      }
      
      // Restart animation
      _animationController.reset();
      _animationController.forward();
    });
  }

  Future<void> _selectDateRange(BuildContext context) async {
    DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      saveText: 'Apply',
    );
    
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _selectedPeriod = 'custom';
        
        // Restart animation
        _animationController.reset();
        _animationController.forward();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);
    
    final provider = Provider.of<TransactionProvider>(context);
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    // Filter transactions by date range
    final transactions = provider.getTransactionsInDateRange(_startDate, _endDate);
    
    // Calculate totals
    final totalIncome = provider.getTotalByTypeInRange(TransactionType.income, _startDate, _endDate);
    final totalExpense = provider.getTotalByTypeInRange(TransactionType.expense, _startDate, _endDate);
    final totalLoan = provider.getTotalByTypeInRange(TransactionType.loan, _startDate, _endDate);
    final balance = totalIncome - totalExpense;
    
    // Get spending by category for pie chart
    final categorySpending = provider.getSpendingByCategory(_startDate, _endDate);
    
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.bar_chart),
            SizedBox(width: 8),
            Text('Statistics'),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(_showTransactionList ? Icons.list : Icons.bar_chart),
            onPressed: () {
              setState(() {
                _showTransactionList = !_showTransactionList;
              });
            },
            tooltip: _showTransactionList ? 'Show Charts' : 'Show Transactions',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () => _selectDateRange(context),
            tooltip: 'Select Date Range',
          ),
        ],
      ),
      body: Column(
        children: [
          // Date filter chips
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFilterChip('week', 'Week'),
                _buildFilterChip('month', 'Month'),
                _buildFilterChip('year', 'Year'),
                _buildFilterChip('custom', 'Custom'),
                if (_selectedPeriod == 'custom')
                  Chip(
                    label: Text(
                      '${DateFormat('MMM d').format(_startDate)} - ${DateFormat('MMM d').format(_endDate)}',
                      style: TextStyle(
                        color: theme.colorScheme.onPrimary,
                        fontSize: 12,
                      ),
                    ),
                    backgroundColor: theme.colorScheme.primary,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
              ],
            ),
          ),
          
          // Summary Cards
          FadeTransition(
            opacity: _fadeAnimation,
            child: SizedBox(
              height: 100,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                children: [
                  _buildSummaryCard('Balance', balance, Colors.blue, Icons.account_balance_wallet),
                  _buildSummaryCard('Income', totalIncome, Colors.green, Icons.arrow_downward),
                  _buildSummaryCard('Expenses', totalExpense, Colors.red, Icons.arrow_upward),
                  _buildSummaryCard('Loans', totalLoan, Colors.orange, Icons.compare_arrows),
                ],
              ),
            ),
          ),
          
          // Main content - either charts or transaction list
          Expanded(
            child: _showTransactionList
                ? _buildTransactionList(transactions, provider)
                : _buildCharts(categorySpending, provider, size, theme),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedPeriod == value;
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ChoiceChip(
        label: Text(
          label,
          style: TextStyle(
            color: isSelected 
                ? theme.colorScheme.onPrimary 
                : theme.colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedColor: theme.colorScheme.primary,
        backgroundColor: theme.colorScheme.surface,
        onSelected: (selected) {
          if (selected) {
            _setPeriod(value);
          }
        },
      ),
    );
  }

  Widget _buildSummaryCard(String title, double amount, Color color, IconData icon) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final formattedAmount = provider.formatCurrency(amount);
    
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 140,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.7), color.withOpacity(0.4)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
            Text(
              formattedAmount,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCharts(Map<String, double> categorySpending, TransactionProvider provider, Size size, ThemeData theme) {
    if (categorySpending.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart_outlined,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No data for selected period',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.withOpacity(0.8),
              ),
            ),
          ],
        ),
      );
    }
    
    // Convert the map to a list for the pie chart
    final pieData = categorySpending.entries.map((entry) {
      final category = provider.getCategoryById(entry.key);
      return MapEntry(
        category?.name ?? 'Other',
        entry.value,
      );
    }).toList();
    
    // Sort by amount (descending)
    pieData.sort((a, b) => b.value.compareTo(a.value));
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Spending by Category',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 24),
          Center(
            child: SizedBox(
              height: size.width * 0.7,
              width: size.width * 0.7,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return PieChart(
                          PieChartData(
                            sections: _buildPieChartSections(pieData, provider),
                            centerSpaceRadius: 40,
                            sectionsSpace: 2,
                            startDegreeOffset: -90,
                            pieTouchData: PieTouchData(enabled: true),
                          ),
                          swapAnimationDuration: const Duration(milliseconds: 800),
                          swapAnimationCurve: Curves.easeInOutQuint,
                        );
                      },
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Total',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          provider.formatCurrency(
                            categorySpending.values.fold(0, (sum, value) => sum + value),
                          ),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'Category Breakdown',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ...pieData.map((entry) => _buildCategoryLegendItem(entry.key, entry.value, provider)),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(List<MapEntry<String, double>> data, TransactionProvider provider) {
    final total = data.fold(0.0, (sum, item) => sum + item.value);
    final colors = [
      const Color(0xFF3F51B5),
      const Color(0xFF2196F3),
      const Color(0xFF03A9F4),
      const Color(0xFF00BCD4),
      const Color(0xFF009688),
      const Color(0xFF4CAF50),
      const Color(0xFF8BC34A),
      const Color(0xFFCDDC39),
      const Color(0xFFFFEB3B),
      const Color(0xFFFFC107),
      const Color(0xFFFF9800),
      const Color(0xFFFF5722),
    ];
    
    return List.generate(
      min(data.length, colors.length),
      (i) {
        final percentage = data[i].value / total;
        final value = data[i].value;
        final color = colors[i % colors.length];
        
        // Scale the section radius based on animation
        final radius = 60.0 * _animationController.value;
        
        return PieChartSectionData(
          value: value,
          title: '${(percentage * 100).toStringAsFixed(1)}%',
          titleStyle: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
          radius: radius,
          color: color,
        );
      },
    );
  }

  Widget _buildCategoryLegendItem(String categoryName, double amount, TransactionProvider provider) {
    final formattedAmount = provider.formatCurrency(amount);
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: _getCategoryColor(categoryName),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              categoryName,
              style: theme.textTheme.bodyLarge,
            ),
          ),
          Text(
            formattedAmount,
            style: theme.textTheme.bodyLarge!.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String categoryName) {
    final colors = {
      'Food': const Color(0xFF3F51B5),
      'Transport': const Color(0xFF2196F3),
      'Shopping': const Color(0xFFFF5722),
      'Utilities': const Color(0xFF4CAF50),
      'Entertainment': const Color(0xFF9C27B0),
      'Health': const Color(0xFF009688),
      'Salary': const Color(0xFF4CAF50),
      'Gift': const Color(0xFF9C27B0),
      'Interest': const Color(0xFF2196F3),
      'Loan': const Color(0xFFFF9800),
    };
    
    return colors[categoryName] ?? const Color(0xFF607D8B);
  }

  Widget _buildTransactionList(List<Transaction> transactions, TransactionProvider provider) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No transactions for selected period',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.withOpacity(0.8),
              ),
            ),
          ],
        ),
      );
    }
    
    // Group transactions by date
    final groupedTransactions = <DateTime, List<Transaction>>{};
    for (final transaction in transactions) {
      final date = DateTime(transaction.date.year, transaction.date.month, transaction.date.day);
      if (groupedTransactions.containsKey(date)) {
        groupedTransactions[date]!.add(transaction);
      } else {
        groupedTransactions[date] = [transaction];
      }
    }
    
    // Sort dates in descending order
    final sortedDates = groupedTransactions.keys.toList()
      ..sort((a, b) => b.compareTo(a));
    
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: sortedDates.length,
      itemBuilder: (context, dateIndex) {
        final date = sortedDates[dateIndex];
        final dayTransactions = groupedTransactions[date]!;
        
        // Sort transactions by time for each day
        dayTransactions.sort((a, b) => b.date.compareTo(a.date));
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                _formatDate(date),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            ...dayTransactions.map((transaction) => _buildTransactionItem(transaction, provider)),
          ],
        );
      },
    );
  }

  Widget _buildTransactionItem(Transaction transaction, TransactionProvider provider) {
    final theme = Theme.of(context);
    final category = provider.getCategoryById(transaction.categoryId);
    final formattedAmount = CurrencyUtils.formatCurrencyAmount(transaction.amount, transaction.currencyCode);
    
    Color amountColor;
    switch (transaction.type) {
      case TransactionType.expense:
        amountColor = Colors.red;
        break;
      case TransactionType.income:
        amountColor = Colors.green;
        break;
      case TransactionType.loan:
        amountColor = Colors.orange;
        break;
    }

    return SlideTransition(
      position: Tween<Offset>(begin: const Offset(-0.5, 0), end: Offset.zero).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeOutQuad,
        ),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _showTransactionDetails(transaction),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Category icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(category?.colorValue ?? 0xFF9E9E9E).withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      category?.icon ?? '📝',
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Transaction details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category?.name ?? 'Other',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (transaction.description.isNotEmpty)
                        Text(
                          transaction.description,
                          style: TextStyle(
                            color: theme.brightness == Brightness.light 
                                ? Colors.black54 
                                : Colors.grey.shade300,
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
                
                // Amount
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      transaction.type == TransactionType.expense 
                          ? '-$formattedAmount' 
                          : formattedAmount,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: amountColor,
                      ),
                    ),
                    Text(
                      DateFormat('h:mm a').format(transaction.date),
                      style: TextStyle(
                        fontSize: 12,
                        color: theme.brightness == Brightness.light 
                            ? Colors.black54 
                            : Colors.grey.shade300,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showTransactionDetails(Transaction transaction) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final category = provider.getCategoryById(transaction.categoryId);
    final theme = Theme.of(context);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Transaction Details',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Color(category?.colorValue ?? 0xFF9E9E9E).withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        category?.icon ?? '📝',
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category?.name ?? 'Other',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        Text(
                          transaction.description,
                          style: TextStyle(
                            color: theme.brightness == Brightness.light 
                                ? Colors.black54 
                                : Colors.grey.shade300,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Amount',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    CurrencyUtils.formatCurrencyAmount(transaction.amount, transaction.currencyCode),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: _getTransactionTypeColor(transaction.type),
                    ),
                  ),
                ],
              ),
              const Divider(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Date',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    DateFormat('MMMM d, yyyy').format(transaction.date),
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Time',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    DateFormat('h:mm a').format(transaction.date),
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Type',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    transaction.type.name.toUpperCase(),
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: _getTransactionTypeColor(transaction.type),
                    ),
                  ),
                ],
              ),
              if (transaction.tags.isNotEmpty) ...[              
                const SizedBox(height: 24),
                Text(
                  'Tags',
                  style: TextStyle(
                    color: theme.brightness == Brightness.light 
                        ? Colors.black54 
                        : Colors.grey.shade300,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: transaction.tags.map((tag) {
                    return Chip(
                      label: Text('#$tag'),
                      backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                      side: BorderSide(
                        color: theme.colorScheme.primary.withOpacity(0.2),
                      ),
                      padding: EdgeInsets.zero,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    );
                  }).toList(),
                ),
              ],
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () async {
                        // Close the modal
                        Navigator.of(context).pop();
                        
                        // Show edit dialog
                        final result = await showDialog<Transaction>(
                          context: context,
                          builder: (context) => TransactionEditDialog(transaction: transaction),
                        );
                        
                        if (result != null) {
                          // Update the transaction
                          await provider.updateTransaction(result);
                          
                          // Show success snackbar
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Transaction updated'),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        provider.deleteTransaction(transaction.id);
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('Delete'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.income:
        return Colors.green;
      case TransactionType.loan:
        return Colors.orange;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    
    if (date == today) {
      return 'Today';
    } else if (date == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('EEEE, MMMM d').format(date);
    }
  }
}

// Helper function for min
int min(int a, int b) => a < b ? a : b;
</file>

<file path="services/parser/category_finder_service.dart">
import '../../models/transaction_model.dart';
import '../storage_service.dart';
import 'learned_category_storage.dart';
import 'learned_association_service.dart';
import 'category_keyword_map.dart';

/// Service for finding transaction categories using keyword matching and learned associations
class CategoryFinderService {
  final LearnedCategoryStorage _learnedStorage;
  LearnedAssociationService? _learnedAssociationService;

  CategoryFinderService(StorageService storageService)
      : _learnedStorage = LearnedCategoryStorage(storageService) {
    _initializeLearnedAssociationService(storageService);
  }

  /// Initialize the learned association service
  void _initializeLearnedAssociationService(StorageService storageService) async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
    } catch (e) {
      print('Failed to initialize learned association service in CategoryFinderService: $e');
    }
  }

  /// Find category for the given text and transaction type
  /// Returns null if no category can be determined (triggering user selection)
  Future<String?> findCategory(String remainingText, TransactionType type) async {
    if (remainingText.trim().isEmpty) return null;

    // First check new unified learned associations
    if (_learnedAssociationService != null) {
      final learnedAssociation = await _learnedAssociationService!.getAssociation(remainingText);
      if (learnedAssociation?.categoryId != null) {
        return learnedAssociation!.categoryId;
      }
    }

    // Fall back to legacy learned storage for backward compatibility
    final learnedCategory = await _learnedStorage.getLearnedCategory(remainingText);
    if (learnedCategory != null) {
      return learnedCategory;
    }

    // Fall back to keyword matching
    final keywordCategory = findCategoryByKeywords(remainingText);
    if (keywordCategory != null) {
      return keywordCategory;
    }

    // No category found
    return null;
  }

  /// Save a user's category selection for future learning
  Future<void> learnCategory(String text, String categoryId) async {
    // Use new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(text, categoryId: categoryId);
    } else {
      // Fall back to legacy storage for backward compatibility
      await _learnedStorage.saveLearnedCategory(text, categoryId);
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, String>> getAllLearnedCategories() async {
    return await _learnedStorage.getAllLearnedCategories();
  }

  /// Clear all learned data
  Future<void> clearLearnedData() async {
    await _learnedStorage.clearLearnedData();
  }

  /// Export learned data for debugging
  Future<String?> exportLearnedData() async {
    return await _learnedStorage.exportLearnedData();
  }

  /// Get available category IDs for keyword matching
  List<String> getAvailableCategoryIds() {
    return getAllCategoryIds();
  }

  /// Get keywords for a specific category
  List<String> getKeywordsForCategory(String categoryId) {
    return categoryKeywords[categoryId] ?? [];
  }
}
</file>

<file path="services/parser/category_keyword_map.dart">
/// Multilingual keyword mappings for category detection
/// Maps category IDs to lists of keywords in English, Spanish, and German
const Map<String, List<String>> categoryKeywords = {
  // Food & Drink
  'food': [
    // English
    'food', 'restaurant', 'cafe', 'coffee', 'lunch', 'dinner', 'breakfast',
    'pizza', 'burger', 'sandwich', 'meal', 'eat', 'drink', 'bar', 'pub',
    'snack', 'grocery', 'supermarket', 'kitchen', 'cooking', 'recipe',
    'mcdonalds', 'starbucks', 'subway', 'kfc', 'dominos', 'taco bell',
    'wendys', 'chipotle', 'panera', 'dunkin', 'chick-fil-a',
    // Spanish
    'comida', 'restaurante', 'almuerzo', 'cena', 'desayuno', 'bebida',
    'supermercado', 'cocina', 'comer', 'beber', 'cafeteria', 'bar',
    // German
    'essen', 'restaurant', 'mittagessen', 'abendessen', 'frühstück',
    'trinken', 'supermarkt', 'küche', 'café', 'bar'
  ],

  // Transportation
  'transport': [
    // English
    'transport', 'taxi', 'uber', 'lyft', 'bus', 'train', 'subway', 'metro',
    'gas', 'fuel', 'petrol', 'parking', 'toll', 'car', 'vehicle',
    'airline', 'flight', 'airport', 'ticket', 'travel', 'trip',
    'bike', 'bicycle', 'scooter', 'motorcycle', 'boat', 'ferry',
    // Spanish
    'transporte', 'taxi', 'autobús', 'tren', 'metro', 'gasolina',
    'combustible', 'estacionamiento', 'peaje', 'coche', 'viaje',
    'vuelo', 'aeropuerto', 'billete', 'bicicleta',
    // German
    'transport', 'taxi', 'bus', 'zug', 'u-bahn', 'benzin', 'kraftstoff',
    'parkplatz', 'maut', 'auto', 'fahrzeug', 'flug', 'flughafen',
    'ticket', 'reise', 'fahrrad'
  ],

  // Shopping
  'shopping': [
    // English
    'shopping', 'store', 'mall', 'market', 'retail', 'purchase', 'buy',
    'clothes', 'clothing', 'fashion', 'shoes', 'accessories', 'jewelry',
    'electronics', 'gadget', 'phone', 'computer', 'laptop', 'tablet',
    'amazon', 'ebay', 'walmart', 'target', 'costco', 'best buy',
    'apple store', 'nike', 'adidas', 'zara', 'h&m',
    // Spanish
    'compras', 'tienda', 'centro comercial', 'mercado', 'comprar',
    'ropa', 'zapatos', 'accesorios', 'joyería', 'electrónicos',
    'teléfono', 'computadora', 'portátil',
    // German
    'einkaufen', 'geschäft', 'einkaufszentrum', 'markt', 'kaufen',
    'kleidung', 'schuhe', 'accessoires', 'schmuck', 'elektronik',
    'telefon', 'computer', 'laptop'
  ],

  // Utilities
  'utilities': [
    // English
    'electric', 'electricity', 'gas', 'water', 'internet', 'phone',
    'cable', 'tv', 'streaming', 'netflix', 'spotify', 'subscription',
    'utility', 'bill', 'payment', 'service', 'provider',
    'verizon', 'att', 'comcast', 'spectrum', 'hulu', 'disney+',
    // Spanish
    'electricidad', 'agua', 'internet', 'teléfono', 'cable',
    'servicios', 'factura', 'pago', 'proveedor', 'suscripción',
    // German
    'strom', 'elektrizität', 'wasser', 'internet', 'telefon',
    'kabel', 'rechnung', 'zahlung', 'anbieter', 'abonnement'
  ],

  // Entertainment
  'entertainment': [
    // English
    'movie', 'cinema', 'theater', 'concert', 'music', 'game', 'gaming',
    'entertainment', 'fun', 'party', 'club', 'festival', 'event',
    'sport', 'gym', 'fitness', 'recreation', 'hobby', 'leisure',
    'netflix', 'spotify', 'xbox', 'playstation', 'steam',
    // Spanish
    'película', 'cine', 'teatro', 'concierto', 'música', 'juego',
    'entretenimiento', 'diversión', 'fiesta', 'club', 'festival',
    'deporte', 'gimnasio', 'recreación', 'hobby',
    // German
    'film', 'kino', 'theater', 'konzert', 'musik', 'spiel',
    'unterhaltung', 'spaß', 'party', 'club', 'festival',
    'sport', 'fitnessstudio', 'erholung', 'hobby'
  ],

  // Health & Medical
  'health': [
    // English
    'health', 'medical', 'doctor', 'hospital', 'pharmacy', 'medicine',
    'prescription', 'dental', 'dentist', 'clinic', 'treatment',
    'insurance', 'wellness', 'fitness', 'vitamin', 'supplement',
    'cvs', 'walgreens', 'rite aid', 'urgent care',
    // Spanish
    'salud', 'médico', 'hospital', 'farmacia', 'medicina',
    'receta', 'dental', 'dentista', 'clínica', 'tratamiento',
    'seguro', 'bienestar', 'vitamina', 'suplemento',
    // German
    'gesundheit', 'medizinisch', 'arzt', 'krankenhaus', 'apotheke',
    'medizin', 'rezept', 'zahnarzt', 'klinik', 'behandlung',
    'versicherung', 'wellness', 'vitamin', 'nahrungsergänzung'
  ],

  // Education
  'education': [
    // English
    'school', 'university', 'college', 'education', 'tuition', 'course',
    'class', 'book', 'textbook', 'supplies', 'learning', 'study',
    'teacher', 'professor', 'student', 'academic', 'training',
    // Spanish
    'escuela', 'universidad', 'colegio', 'educación', 'matrícula',
    'curso', 'clase', 'libro', 'suministros', 'aprendizaje',
    'maestro', 'profesor', 'estudiante', 'entrenamiento',
    // German
    'schule', 'universität', 'hochschule', 'bildung', 'studiengebühren',
    'kurs', 'klasse', 'buch', 'lehrbuch', 'lernen', 'studium',
    'lehrer', 'professor', 'student', 'ausbildung'
  ],

  // Home & Garden
  'home': [
    // English
    'home', 'house', 'rent', 'mortgage', 'furniture', 'decoration',
    'garden', 'tools', 'repair', 'maintenance', 'cleaning', 'supplies',
    'ikea', 'home depot', 'lowes', 'bed bath beyond',
    // Spanish
    'casa', 'hogar', 'alquiler', 'hipoteca', 'muebles', 'decoración',
    'jardín', 'herramientas', 'reparación', 'mantenimiento', 'limpieza',
    // German
    'haus', 'zuhause', 'miete', 'hypothek', 'möbel', 'dekoration',
    'garten', 'werkzeuge', 'reparatur', 'wartung', 'reinigung'
  ],

  // Personal Care
  'personal': [
    // English
    'beauty', 'cosmetics', 'haircut', 'salon', 'spa', 'massage',
    'personal care', 'hygiene', 'skincare', 'makeup', 'perfume',
    'barber', 'nail', 'manicure', 'pedicure',
    // Spanish
    'belleza', 'cosméticos', 'corte de pelo', 'salón', 'cuidado personal',
    'higiene', 'maquillaje', 'perfume', 'barbero', 'uñas',
    // German
    'schönheit', 'kosmetik', 'haarschnitt', 'salon', 'körperpflege',
    'hygiene', 'hautpflege', 'makeup', 'parfüm', 'friseur', 'nagel'
  ],

  // Business & Professional
  'business': [
    // English
    'business', 'office', 'supplies', 'professional', 'meeting',
    'conference', 'software', 'service', 'consulting', 'legal',
    'accounting', 'tax', 'bank', 'fee', 'commission',
    // Spanish
    'negocio', 'oficina', 'suministros', 'profesional', 'reunión',
    'conferencia', 'software', 'servicio', 'consultoría', 'legal',
    'contabilidad', 'impuesto', 'banco', 'tarifa', 'comisión',
    // German
    'geschäft', 'büro', 'büromaterial', 'professionell', 'besprechung',
    'konferenz', 'software', 'service', 'beratung', 'rechtlich',
    'buchhaltung', 'steuer', 'bank', 'gebühr', 'provision'
  ]
};

/// Utility function to find category by keywords with enhanced conflict resolution
String? findCategoryByKeywords(String text) {
  if (text.isEmpty) return null;

  final normalizedText = text.toLowerCase().trim();

  // Split text into words for matching
  final words = normalizedText.split(RegExp(r'\s+'));

  // Score each category based on keyword matches
  final categoryScores = <String, double>{};
  final matchedKeywords = <String, List<String>>{};

  for (final entry in categoryKeywords.entries) {
    final categoryId = entry.key;
    final keywords = entry.value;
    double score = 0;
    final matched = <String>[];

    // Check if any keyword matches
    for (final keyword in keywords) {
      final keywordLower = keyword.toLowerCase();

      // Exact word match gets highest score
      if (words.contains(keywordLower)) {
        // Give extra points for longer/more specific keywords
        double baseScore = 10 + (keywordLower.length > 5 ? 3 : 0);

        // Apply category-specific bonuses for food-related keywords
        if (categoryId == 'food' && _isFoodSpecificKeyword(keywordLower)) {
          baseScore += 5; // Bonus for food-specific keywords like 'grocery'
        }

        score += baseScore;
        matched.add(keywordLower);
        continue;
      }

      // Brand/proper noun exact matches get very high score
      if (keywordLower.length > 3 && _isBrandName(keywordLower)) {
        if (normalizedText.contains(keywordLower)) {
          score += 15;
          matched.add(keywordLower);
          continue;
        }
      }

      // Partial word match gets medium score
      bool partialMatch = false;
      for (final word in words) {
        if (word.length >= 3 && keywordLower.length >= 3) {
          if (word.contains(keywordLower) || keywordLower.contains(word)) {
            score += 6;
            matched.add(keywordLower);
            partialMatch = true;
            break;
          }
        }
      }

      if (partialMatch) continue;

      // Full text contains keyword gets low score
      if (normalizedText.contains(keywordLower) && keywordLower.length >= 3) {
        score += 2;
        matched.add(keywordLower);
      }
    }

    if (score > 0) {
      categoryScores[categoryId] = score;
      matchedKeywords[categoryId] = matched;
    }
  }

  // Apply compound phrase detection and priority rules
  categoryScores.forEach((categoryId, score) {
    final matched = matchedKeywords[categoryId] ?? [];

    // Detect compound phrases and apply bonuses
    if (_hasCompoundPhrase(normalizedText, categoryId, matched)) {
      categoryScores[categoryId] = score + 8; // Bonus for compound phrases
    }

    // Apply category priority rules for conflicts
    if (_shouldApplyCategoryPriority(normalizedText, categoryId, categoryScores.keys.toList())) {
      categoryScores[categoryId] = score + 6; // Priority bonus
    }
  });

  // Return the category with the highest score, but only if it has a significant score
  if (categoryScores.isEmpty) return null;

  final bestCategory = categoryScores.entries
      .reduce((a, b) => a.value > b.value ? a : b);

  // Only return a match if the score is significant enough
  if (bestCategory.value < 6) return null;

  // Enhanced conflict resolution with improved tie-breaking
  final sortedScores = categoryScores.entries.toList()
    ..sort((a, b) => b.value.compareTo(a.value));

  if (sortedScores.length > 1) {
    final best = sortedScores[0];
    final second = sortedScores[1];

    // Enhanced tie-breaking logic with category-specific rules
    // Special case: food vs shopping conflict resolution (check even if scores aren't close)
    if ((best.key == 'food' && second.key == 'shopping') ||
        (best.key == 'shopping' && second.key == 'food')) {
      // Check for food-specific indicators in compound phrases
      if (_containsFoodShoppingPhrase(normalizedText)) {
        return 'food'; // Prefer food for "grocery shopping", "food shopping"
      }
      // Check for shopping-specific indicators
      if (_containsShoppingSpecificPhrase(normalizedText)) {
        return 'shopping'; // Prefer shopping for "clothes shopping", "electronics shopping"
      }
    }

    if (best.value < second.value + 4) {
      // If still ambiguous and scores are very close, return null
      if (best.value < second.value + 2 && best.value < 12) {
        return null;
      }
    }
  }

  return bestCategory.key;
}

/// Check if a keyword is a brand name or proper noun
bool _isBrandName(String keyword) {
  final brandKeywords = [
    'mcdonalds', 'starbucks', 'subway', 'kfc', 'dominos', 'taco bell',
    'wendys', 'chipotle', 'panera', 'dunkin', 'chick-fil-a',
    'amazon', 'ebay', 'walmart', 'target', 'costco', 'best buy',
    'apple store', 'nike', 'adidas', 'zara', 'h&m', 'uber', 'lyft'
  ];
  return brandKeywords.contains(keyword);
}

/// Check if a keyword is food-specific and should get priority
bool _isFoodSpecificKeyword(String keyword) {
  final foodSpecificKeywords = [
    'grocery', 'supermarket', 'restaurant', 'cafe', 'coffee',
    'meal', 'food', 'kitchen', 'cooking', 'recipe'
  ];
  return foodSpecificKeywords.contains(keyword);
}

/// Detect compound phrases for category bonuses
bool _hasCompoundPhrase(String text, String categoryId, List<String> matchedKeywords) {
  if (categoryId == 'food') {
    // Food compound phrases
    final foodCompounds = [
      'grocery shopping', 'food shopping', 'restaurant meal',
      'coffee shop', 'fast food', 'takeout food'
    ];
    return foodCompounds.any((phrase) => text.contains(phrase));
  }

  if (categoryId == 'shopping') {
    // Shopping compound phrases
    final shoppingCompounds = [
      'clothes shopping', 'electronics shopping', 'online shopping',
      'retail store', 'shopping mall', 'department store'
    ];
    return shoppingCompounds.any((phrase) => text.contains(phrase));
  }

  return false;
}

/// Apply category priority rules for conflict resolution
bool _shouldApplyCategoryPriority(String text, String categoryId, List<String> allCategories) {
  // Food category gets priority when both food and shopping keywords are present
  if (categoryId == 'food' && allCategories.contains('shopping')) {
    final foodPriorityIndicators = [
      'grocery', 'restaurant', 'cafe', 'meal', 'food', 'eat', 'drink'
    ];
    return foodPriorityIndicators.any((indicator) => text.contains(indicator));
  }

  return false;
}

/// Check for food-shopping compound phrases that should prefer food category
bool _containsFoodShoppingPhrase(String text) {
  final foodShoppingPhrases = [
    'grocery shopping', 'food shopping', 'supermarket shopping',
    'restaurant shopping', 'cafe shopping'
  ];

  // Direct phrase matching
  if (foodShoppingPhrases.any((phrase) => text.contains(phrase))) {
    return true;
  }

  // Check for food-related words combined with shopping
  final foodWords = ['grocery', 'food', 'supermarket', 'restaurant', 'cafe'];
  final shoppingWords = ['shopping', 'shop', 'store'];

  // Check if text contains both food and shopping words
  final hasFoodWord = foodWords.any((word) => text.contains(word));
  final hasShoppingWord = shoppingWords.any((word) => text.contains(word));

  return hasFoodWord && hasShoppingWord;
}

/// Check for shopping-specific compound phrases that should prefer shopping category
bool _containsShoppingSpecificPhrase(String text) {
  final shoppingSpecificPhrases = [
    'clothes shopping', 'clothing shopping', 'electronics shopping',
    'gadget shopping', 'phone shopping', 'computer shopping',
    'fashion shopping', 'shoe shopping', 'jewelry shopping'
  ];
  return shoppingSpecificPhrases.any((phrase) => text.contains(phrase));
}

/// Get keywords for a specific category
List<String> getKeywordsForCategory(String categoryId) {
  return categoryKeywords[categoryId] ?? [];
}

/// Get all available category IDs that have keywords
List<String> getAllCategoryIds() {
  return categoryKeywords.keys.toList();
}
</file>

<file path="services/parser/entity_extractor_base.dart">
/// Abstract base classes for entity extraction to enable dependency injection and testing
/// This provides a clean abstraction layer over ML Kit entity extraction functionality

/// Enumeration of entity types that can be extracted
enum EntityType {
  money,
  dateTime,
  address,
  email,
  phone,
  url,
  other,
}

/// Abstract base class representing an entity annotation
/// This defines the minimal interface needed by the parser service
abstract class EntityAnnotationBase {
  /// The text content of the entity
  String get text;
  
  /// Start position of the entity in the original text
  int get start;
  
  /// End position of the entity in the original text
  int get end;
  
  /// Type of the entity (money, datetime, etc.)
  EntityType get entityType;
}

/// Abstract base class for entity extraction functionality
/// This defines the interface that both real ML Kit and mock implementations must follow
abstract class EntityExtractorBase {
  /// Extract entities from the given text
  /// Returns a list of entity annotations found in the text
  Future<List<EntityAnnotationBase>> annotateText(String text);
  
  /// Close and dispose of any resources used by the extractor
  Future<void> close();
  
  /// Check if the extractor is properly initialized and ready to use
  bool get isInitialized;
}
</file>

<file path="services/parser/fallback_parser_service.dart">
import 'dart:ui';
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../models/localization_data.dart';
import '../../utils/currency_utils.dart';
import '../../utils/amount_utils.dart';
import '../storage_service.dart';
import '../localization_service.dart';
import 'category_finder_service.dart';
import 'learned_association_service.dart';

/// Fallback transaction parser that uses regex-based parsing when ML Kit is not available
class FallbackParserService {
  final CategoryFinderService _categoryFinder;
  final StorageService _storageService;
  final LocalizationService _localizationService;
  LearnedAssociationService? _learnedAssociationService;
  final Uuid _uuid = const Uuid();

  FallbackParserService(
    StorageService storageService, {
    LocalizationService? localizationService,
  })  : _categoryFinder = CategoryFinderService(storageService),
        _storageService = storageService,
        _localizationService = localizationService ?? LocalizationService.instance {
    _initializeLearnedAssociationService(storageService);
  }

  /// Initialize the learned association service
  void _initializeLearnedAssociationService(StorageService storageService) async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
    } catch (e) {
      print('Failed to initialize learned association service in FallbackParserService: $e');
    }
  }

  /// Parse transaction using regex-based approach with localization support
  Future<ParseResult> parseTransaction(String text, {Locale? locale}) async {
    try {
      // Get current locale or use default
      final currentLocale = locale ?? const Locale('en');

      // Load localization patterns
      final localizationData = await _localizationService.getPatternsForLocale(currentLocale);

      // Normalize text for easier parsing
      final normalizedText = text.toLowerCase().trim();

      // Try to extract amount and currency first to detect negative amounts
      final amountResult = _extractAmount(normalizedText, localizationData);
      if (amountResult == null || amountResult['amount'] == null) {
        return ParseResult.failed(
          _createFallbackTransaction(text),
          'Could not extract amount from text'
        );
      }

      final amount = amountResult['amount'] as double;
      final isNegativeAmount = amountResult['isNegative'] as bool? ?? false;
      String currencyCode = amountResult['currency'] as String? ?? await _storageService.getDefaultCurrency();

      // Try to detect transaction type with negative amount information
      final detectedType = _detectTransactionType(normalizedText, localizationData, isNegativeAmount: isNegativeAmount);

      // If transaction type is unclear, return needsType status
      if (detectedType == null) {
        // Create partial transaction with default expense type for type disambiguation
        final partialTransaction = Transaction(
          id: _uuid.v4(),
          amount: amount,
          type: TransactionType.expense, // Default type, will be updated by user selection
          categoryId: 'unknown', // Use placeholder for unknown categories
          date: DateTime.now(),
          description: _createDescription(text),
          tags: _extractTags(text),
          currencyCode: currencyCode,
        );

        return ParseResult.needsType(partialTransaction);
      }

      // Find category using the category finder service
      final categoryId = await _categoryFinder.findCategory(text, detectedType);

      // Create the transaction - use 'unknown' placeholder when no category found
      final transaction = Transaction(
        id: _uuid.v4(),
        amount: amount,
        type: detectedType,
        categoryId: categoryId ?? 'unknown', // Use placeholder for unknown categories
        date: DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: currencyCode,
      );

      // Return result indicating if category selection is needed
      if (categoryId == null) {
        return ParseResult.needsCategory(transaction);
      } else {
        return ParseResult.success(transaction);
      }

    } catch (e) {
      return ParseResult.failed(
        _createFallbackTransaction(text),
        'Parsing failed: $e'
      );
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    // Use new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(text, categoryId: categoryId);
    } else {
      // Fall back to category finder for backward compatibility
      await _categoryFinder.learnCategory(text, categoryId);
    }
  }

  /// Detect transaction type from text using localization data
  TransactionType? _detectTransactionType(String text, LocalizationData localizationData, {bool isNegativeAmount = false}) {
    // Check for negative sign or minus at the beginning which indicates expense
    if (RegExp(r'^\s*-').hasMatch(text) || isNegativeAmount) {
      return TransactionType.expense;
    }

    // Build dynamic regex patterns from localization data
    final incomePattern = _buildKeywordPattern(localizationData.incomeKeywords);
    final loanPattern = _buildKeywordPattern(localizationData.loanKeywords);
    final expensePattern = _buildKeywordPattern(localizationData.expenseKeywords);
    final currencyPattern = _buildKeywordPattern(localizationData.currencySymbols);

    // Income patterns (check these first as they're more specific)
    if (incomePattern.hasMatch(text)) {
      return TransactionType.income;
    }

    // Loan patterns
    if (loanPattern.hasMatch(text)) {
      return TransactionType.loan;
    }

    // Expense patterns (more general, check after specific income/loan patterns)
    if (expensePattern.hasMatch(text)) {
      return TransactionType.expense;
    }

    // Special case: 'for' keyword typically indicates expense unless preceded by income keyword
    final forPattern = localizationData.specialPatterns['for_keyword'];
    final incomeBeforeForPattern = localizationData.specialPatterns['income_before_for'];

    if (forPattern != null && RegExp(forPattern).hasMatch(text)) {
      if (incomeBeforeForPattern == null || !RegExp(incomeBeforeForPattern).hasMatch(text)) {
        return TransactionType.expense;
      }
    }

    // Default to expense if contains currency symbols
    if (currencyPattern.hasMatch(text)) {
      return TransactionType.expense;
    }

    return null;
  }

  /// Build a regex pattern from a list of keywords
  RegExp _buildKeywordPattern(List<String> keywords) {
    if (keywords.isEmpty) {
      return RegExp(r'(?!)'); // Never matches
    }

    // Escape special regex characters and join with OR
    final escapedKeywords = keywords.map((keyword) => RegExp.escape(keyword)).join('|');
    return RegExp('($escapedKeywords)', caseSensitive: false);
  }
  
  /// Extract amount from text using localization data
  Map<String, dynamic>? _extractAmount(String text, LocalizationData localizationData) {
    // Handle negative amount pattern first (like -500$ for toys)
    bool isNegative = false;
    String processedText = text;

    if (text.startsWith('-')) {
      isNegative = true;
      // Remove the leading minus for easier parsing
      processedText = text.substring(1).trim();
    }

    // Extract positive amount from processed text
    final result = _extractPositiveAmount(processedText, localizationData);
    if (result != null) {
      // Add negative flag to the result
      result['isNegative'] = isNegative;
    }

    return result;
  }
  
  /// Helper to extract positive amount values from text using localization data with abbreviation support
  Map<String, dynamic>? _extractPositiveAmount(String text, LocalizationData localizationData) {
    // First try AmountUtils with localization separators for abbreviation support
    final amountResult = AmountUtils.extractAmountFromText(
      text,
      thousandsSeparator: localizationData.thousandsSeparator,
      decimalSeparator: localizationData.decimalSeparator,
    );

    if (amountResult != null) {
      // AmountUtils found an amount, preserve existing currency detection logic
      String? currency = amountResult['currency'] as String?;
      if (currency == null) {
        currency = _extractCurrency(text, localizationData);
      }

      return {
        'amount': amountResult['amount'] as double,
        'currency': currency,
      };
    }

    // Fallback to original regex-based approach for backward compatibility
    // Build currency symbols pattern from localization data
    final currencySymbolsPattern = localizationData.currencySymbols.map((s) => RegExp.escape(s)).join('|');

    // Build regex pattern that respects localization separators
    final decimalSep = RegExp.escape(localizationData.decimalSeparator);
    final thousandsSep = RegExp.escape(localizationData.thousandsSeparator);

    // Create flexible amount regex that handles different separator configurations
    final amountRegex = RegExp(
      r'(' + currencySymbolsPattern + r')?\s?(\d+(?:' + thousandsSep + r'\d{3})*(?:' + decimalSep + r'\d{1,2})?)\s?(?:dollars|USD|euros?|EUR|pounds?|GBP|yen|JPY|yuan|CNY|rupees?|INR|rubles?|RUB|won|KRW|pesos?|MXN|PHP|dong|VND|baht|THB|lira|TRY|shekel|ILS|reais?|BRL|SGD|HKD|AUD|CAD|NZD|' + currencySymbolsPattern + r')?'
    );
    final match = amountRegex.firstMatch(text);

    if (match != null) {
      final currencySymbol = match.group(1);
      final amountString = match.group(2)!;

      // Convert amount string to double respecting localization separators
      final normalizedAmount = _normalizeAmountString(amountString, localizationData);
      final amount = double.tryParse(normalizedAmount);

      if (amount != null) {
        String? currency = _extractCurrency(text, localizationData);
        if (currency == null && currencySymbol != null) {
          currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
        }

        return {
          'amount': amount,
          'currency': currency,
        };
      }
    }

    return null;
  }

  /// Normalize amount string by converting localized separators to standard format
  String _normalizeAmountString(String amountString, LocalizationData localizationData) {
    String normalized = amountString;

    // If thousands separator is different from comma, replace it with comma
    if (localizationData.thousandsSeparator != ',') {
      normalized = normalized.replaceAll(localizationData.thousandsSeparator, ',');
    }

    // If decimal separator is different from dot, replace it with dot
    if (localizationData.decimalSeparator != '.') {
      normalized = normalized.replaceAll(localizationData.decimalSeparator, '.');
    }

    // Remove commas for final parsing (standard approach)
    normalized = normalized.replaceAll(',', '');

    return normalized;
  }

  /// Extract currency information from text using localization data
  String? _extractCurrency(String text, LocalizationData localizationData) {
    // Check for currency symbols first with context-aware detection using localization data
    final currencySymbolsPattern = localizationData.currencySymbols.map((s) => RegExp.escape(s)).join('|');
    final symbolRegex = RegExp('($currencySymbolsPattern)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }

    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }

    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Transaction _createFallbackTransaction(String text, {double? amount}) {
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'unknown', // Use placeholder for unknown categories
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: 'USD',
    );
  }
}
</file>

<file path="services/parser/learned_association_service.dart">
import 'dart:convert';
import '../../models/transaction_model.dart';
import '../storage_service.dart';
import 'learned_category_storage.dart';

/// Data class representing a learned association between text and transaction attributes
class LearnedAssociation {
  final TransactionType? type;
  final String? categoryId;
  final double? confirmedAmount;
  final DateTime lastUpdated;
  final int confidence;

  LearnedAssociation({
    this.type,
    this.categoryId,
    this.confirmedAmount,
    required this.lastUpdated,
    this.confidence = 1,
  });

  factory LearnedAssociation.fromJson(Map<String, dynamic> json) {
    return LearnedAssociation(
      type: json['type'] != null ? TransactionType.values.byName(json['type']) : null,
      categoryId: json['categoryId'],
      confirmedAmount: json['confirmedAmount']?.toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      confidence: json['confidence'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type?.name,
      'categoryId': categoryId,
      'confirmedAmount': confirmedAmount,
      'lastUpdated': lastUpdated.toIso8601String(),
      'confidence': confidence,
    };
  }

  LearnedAssociation copyWith({
    TransactionType? type,
    String? categoryId,
    double? confirmedAmount,
    DateTime? lastUpdated,
    int? confidence,
  }) {
    return LearnedAssociation(
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      confirmedAmount: confirmedAmount ?? this.confirmedAmount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      confidence: confidence ?? this.confidence,
    );
  }
}

/// Unified service for managing learned associations between text patterns and transaction attributes
class LearnedAssociationService {
  static const String _storageKey = 'learned_associations';
  static const String _migrationCompletedKey = 'learned_association_migration_completed';
  static LearnedAssociationService? _instance;

  final StorageService _storageService;
  final LearnedCategoryStorage _legacyStorage;
  bool _isInitialized = false;
  bool _migrationCompleted = false;

  LearnedAssociationService._(this._storageService)
      : _legacyStorage = LearnedCategoryStorage(_storageService);

  /// Get singleton instance
  static Future<LearnedAssociationService> getInstance(StorageService storageService) async {
    _instance ??= LearnedAssociationService._(storageService);
    if (!_instance!._isInitialized) {
      await _instance!._initialize();
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    _instance = null;
  }

  /// Initialize the service and perform data migration if needed
  Future<void> _initialize() async {
    if (_isInitialized) return;

    // Check if migration was already completed to avoid redundant work
    final migrationCompleted = _storageService.getBool(_migrationCompletedKey) ?? false;

    // Perform migration from legacy storage if needed
    if (!migrationCompleted) {
      print('Performing learned association migration...');
      final startTime = DateTime.now();

      await _migrateFromLegacyStorage();

      // Mark migration as completed
      await _storageService.setBool(_migrationCompletedKey, true);
      _migrationCompleted = true;

      final duration = DateTime.now().difference(startTime);
      print('Learned association migration completed in ${duration.inMilliseconds}ms');
    } else {
      print('Skipping learned association migration (already completed)');
      _migrationCompleted = true;
    }

    _isInitialized = true;
  }

  /// Learn an association between text and transaction attributes
  Future<void> learn(String text, {TransactionType? type, String? categoryId, double? confirmedAmount}) async {
    if (!_isInitialized) await _initialize();

    if (text.trim().isEmpty || (type == null && categoryId == null && confirmedAmount == null)) {
      return; // Nothing to learn
    }

    try {
      final normalizedText = _normalizeText(text);
      final vendorName = _extractVendorName(normalizedText);
      final keyToStore = vendorName ?? normalizedText;
      
      final storedData = _storageService.getString(_storageKey);
      Map<String, dynamic> associationsMap = {};
      
      if (storedData != null) {
        try {
          associationsMap = jsonDecode(storedData);
        } catch (e) {
          // Storage is corrupted, start fresh
          print('Corrupted storage detected, starting fresh: $e');
          associationsMap = {};
        }
      }
      
      // Get existing association or create new one
      LearnedAssociation? existingAssociation;
      if (associationsMap.containsKey(keyToStore)) {
        existingAssociation = LearnedAssociation.fromJson(associationsMap[keyToStore]);
      }
      
      // Create updated association
      final updatedAssociation = LearnedAssociation(
        type: type ?? existingAssociation?.type,
        categoryId: categoryId ?? existingAssociation?.categoryId,
        confirmedAmount: confirmedAmount ?? existingAssociation?.confirmedAmount,
        lastUpdated: DateTime.now(),
        confidence: (existingAssociation?.confidence ?? 0) + 1,
      );
      
      associationsMap[keyToStore] = updatedAssociation.toJson();
      
      final updatedData = jsonEncode(associationsMap);
      await _storageService.setString(_storageKey, updatedData);
    } catch (e) {
      // Log error but don't throw to avoid breaking the learning flow
      print('Error learning association: $e');
    }
  }

  /// Retrieve a learned association for the given text
  Future<LearnedAssociation?> getAssociation(String text) async {
    if (!_isInitialized) await _initialize();
    
    try {
      final normalizedText = _normalizeText(text);
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return null;

      Map<String, dynamic> associationsMap;
      try {
        associationsMap = jsonDecode(storedData);
      } catch (e) {
        // Storage is corrupted, return null
        print('Corrupted storage detected in getAssociation: $e');
        return null;
      }
      
      // First try exact match
      if (associationsMap.containsKey(normalizedText)) {
        return LearnedAssociation.fromJson(associationsMap[normalizedText]);
      }
      
      // Then try to find a partial match with stored vendor names
      final extractedVendor = _extractVendorName(normalizedText);
      if (extractedVendor != null && associationsMap.containsKey(extractedVendor)) {
        return LearnedAssociation.fromJson(associationsMap[extractedVendor]);
      }
      
      // Finally, check if any stored pattern is contained in the text or vice versa
      for (final entry in associationsMap.entries) {
        final storedPattern = entry.key;
        final associationData = entry.value as Map<String, dynamic>;
        
        // Check both directions and also word-level matches
        if (_isPartialMatch(normalizedText, storedPattern)) {
          return LearnedAssociation.fromJson(associationData);
        }
      }
      
      return null;
    } catch (e) {
      // Log error but don't throw to avoid breaking the parsing flow
      print('Error getting association: $e');
      return null;
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, LearnedAssociation>> getAllAssociations() async {
    if (!_isInitialized) await _initialize();
    
    try {
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return {};

      Map<String, dynamic> associationsMap;
      try {
        associationsMap = jsonDecode(storedData);
      } catch (e) {
        // Storage is corrupted, return empty map
        print('Corrupted storage detected in getAllAssociations: $e');
        return {};
      }
      final result = <String, LearnedAssociation>{};
      
      for (final entry in associationsMap.entries) {
        result[entry.key] = LearnedAssociation.fromJson(entry.value);
      }
      
      return result;
    } catch (e) {
      print('Error getting all associations: $e');
      return {};
    }
  }

  /// Clear all learned data
  Future<void> clearAllData() async {
    await _storageService.remove(_storageKey);
  }

  /// Migrate data from legacy LearnedCategoryStorage
  Future<void> _migrateFromLegacyStorage() async {
    try {
      final legacyData = await _legacyStorage.getAllLearnedCategories();
      
      if (legacyData.isEmpty) return;
      
      // Check if we already have new format data
      final existingData = _storageService.getString(_storageKey);
      if (existingData != null) return; // Migration already done
      
      final associationsMap = <String, dynamic>{};
      
      // Convert legacy category-only data to new format
      for (final entry in legacyData.entries) {
        final association = LearnedAssociation(
          categoryId: entry.value,
          lastUpdated: DateTime.now(),
          confidence: 1,
        );
        associationsMap[entry.key] = association.toJson();
      }
      
      if (associationsMap.isNotEmpty) {
        final migratedData = jsonEncode(associationsMap);
        await _storageService.setString(_storageKey, migratedData);
        print('Migrated ${associationsMap.length} learned categories to new format');
      }
    } catch (e) {
      print('Error during migration: $e');
    }
  }

  /// Normalize text for consistent lookup (same as legacy implementation)
  String _normalizeText(String text) {
    return text
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^\w\s]'), '') // Remove special chars
        .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
  }

  /// Extract vendor name or key phrase from transaction description (enhanced for better matching)
  String? _extractVendorName(String text) {
    // Remove common transaction prefixes and suffixes
    String cleanText = text
        .replaceAll(RegExp(r'^(spent|paid|buy|bought|purchase|dinner|lunch|breakfast)\s+(at|in|from)?\s*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(for|on|at|in)\s+.*$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(restaurant|coffee|shop|store|market|cafe)$', caseSensitive: false), '')
        .trim();

    // Remove amounts and currency symbols to focus on vendor name
    cleanText = cleanText
        .replaceAll(RegExp(r'\$?\d+\.?\d*[kmb]?', caseSensitive: false), '') // Remove amounts like $25.50, 25.50, 25, 100k, 2m
        .replaceAll(RegExp(r'[£€¥₹₽]'), '') // Remove other currency symbols
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();

    // If the cleaned text is meaningful, use it
    if (cleanText.isNotEmpty && cleanText.length > 2) {
      return cleanText;
    }
    
    // Try to extract business name patterns (look for capitalized words or brands)
    final words = text.split(RegExp(r'\s+'));
    
    // Look for common business name patterns
    for (int i = 0; i < words.length; i++) {
      final word = words[i].toLowerCase();
      
      // Skip common transaction words and amounts
      if (['spent', 'paid', 'buy', 'bought', 'purchase', 'at', 'in', 'from', 'for', 'on', 'dinner', 'lunch', 'breakfast'].contains(word) ||
          RegExp(r'^\$?\d+\.?\d*[kmb]?$', caseSensitive: false).hasMatch(word)) {
        continue;
      }

      // Try to build a business name from this position
      final businessWords = <String>[];
      for (int j = i; j < words.length && businessWords.length < 3; j++) { // Reduced from 4 to 3 for more focused names
        final currentWord = words[j].toLowerCase();

        // Skip amounts
        if (RegExp(r'^\$?\d+\.?\d*[kmb]?$', caseSensitive: false).hasMatch(currentWord)) {
          continue;
        }

        // Stop at prepositions that usually end business names
        if (['for', 'on', 'with', 'and', 'at', 'in', 'from'].contains(currentWord) && businessWords.isNotEmpty) {
          break;
        }

        // For brand names, prefer the core name without generic suffixes
        if (['restaurant', 'coffee', 'shop', 'store', 'market', 'cafe'].contains(currentWord)) {
          // If we already have a business name, stop here to get the core brand
          if (businessWords.isNotEmpty) {
            break;
          }
          // Otherwise include it as part of the name
        }

        businessWords.add(currentWord);
      }
      
      if (businessWords.isNotEmpty) {
        final businessName = businessWords.join(' ');
        if (businessName.length > 2 && businessName.length < 30) {
          return businessName;
        }
      }
    }
    
    return null;
  }

  /// Check if two texts match partially using word-level comparison (same as legacy implementation)
  bool _isPartialMatch(String text1, String text2) {
    final words1 = text1.split(' ').where((w) => w.isNotEmpty).toSet();
    final words2 = text2.split(' ').where((w) => w.isNotEmpty).toSet();
    
    // If either text has common words with the other, consider it a match
    if (words1.intersection(words2).isNotEmpty) {
      return true;
    }
    
    // Also check substring matches for shorter patterns
    if (text1.length < text2.length && text2.contains(text1)) {
      return true;
    }
    if (text2.length < text1.length && text1.contains(text2)) {
      return true;
    }
    
    return false;
  }
}
</file>

<file path="services/parser/learned_category_storage.dart">
import 'dart:convert';
import '../storage_service.dart';

/// Service for persisting user-learned category associations
///
/// @deprecated This class is deprecated and will be removed in a future version.
/// Use LearnedAssociationService instead, which provides unified learning for both
/// transaction types and categories with improved data structure and migration support.
///
/// Migration: Data from this service is automatically migrated to LearnedAssociationService
/// when the new service is first initialized.
@Deprecated('Use LearnedAssociationService instead')
class LearnedCategoryStorage {
  static const String _storageKey = 'learned_categories';
  final StorageService _storageService;

  LearnedCategoryStorage(this._storageService);

  /// Retrieve a learned category for the given text
  ///
  /// @deprecated Use LearnedAssociationService.getAssociation() instead
  @Deprecated('Use LearnedAssociationService.getAssociation() instead')
  Future<String?> getLearnedCategory(String text) async {
    try {
      final normalizedText = _normalizeText(text);
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return null;
      
      final Map<String, dynamic> learnedMap = jsonDecode(storedData);
      
      // First try exact match
      if (learnedMap.containsKey(normalizedText)) {
        return learnedMap[normalizedText] as String;
      }
      
      // Then try to find a partial match with stored vendor names
      final extractedVendor = _extractVendorName(normalizedText);
      if (extractedVendor != null && learnedMap.containsKey(extractedVendor)) {
        return learnedMap[extractedVendor] as String;
      }
      
      // Finally, check if any stored pattern is contained in the text or vice versa
      for (final entry in learnedMap.entries) {
        final storedPattern = entry.key;
        final categoryId = entry.value as String;
        
        // Check both directions and also word-level matches
        if (_isPartialMatch(normalizedText, storedPattern)) {
          return categoryId;
        }
      }
      
      return null;
    } catch (e) {
      // Log error if needed, but don't throw to avoid breaking the parsing flow
      return null;
    }
  }

  /// Save a learned category association
  ///
  /// @deprecated Use LearnedAssociationService.learn() instead
  @Deprecated('Use LearnedAssociationService.learn() instead')
  Future<void> saveLearnedCategory(String text, String categoryId) async {
    try {
      final normalizedText = _normalizeText(text);
      final vendorName = _extractVendorName(normalizedText);
      final keyToStore = vendorName ?? normalizedText;
      
      final storedData = _storageService.getString(_storageKey);
      Map<String, dynamic> learnedMap = {};
      
      if (storedData != null) {
        learnedMap = jsonDecode(storedData);
      }
      
      learnedMap[keyToStore] = categoryId;
      
      final updatedData = jsonEncode(learnedMap);
      await _storageService.setString(_storageKey, updatedData);
    } catch (e) {
      // Log error if needed, but don't throw to avoid breaking the saving flow
      rethrow;
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, String>> getAllLearnedCategories() async {
    try {
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return {};
      
      final Map<String, dynamic> learnedMap = jsonDecode(storedData);
      return learnedMap.cast<String, String>();
    } catch (e) {
      return {};
    }
  }

  /// Clear all learned data
  Future<void> clearLearnedData() async {
    await _storageService.remove(_storageKey);
  }

  /// Export learned data as JSON string for debugging
  Future<String?> exportLearnedData() async {
    return _storageService.getString(_storageKey);
  }

  /// Normalize text for consistent lookup
  String _normalizeText(String text) {
    return text
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^\w\s]'), '') // Remove special chars
        .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
  }

  /// Extract vendor name or key phrase from transaction description
  String? _extractVendorName(String text) {
    // Remove common transaction prefixes and suffixes
    final cleanText = text
        .replaceAll(RegExp(r'^(spent|paid|buy|bought|purchase|dinner|lunch|breakfast)\s+(at|in|from)?\s*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(for|on|at|in)\s+.*$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(restaurant|coffee|shop|store|market|cafe)$', caseSensitive: false), '')
        .trim();
    
    // If the cleaned text is meaningful, use it
    if (cleanText.isNotEmpty && cleanText.length > 2) {
      return cleanText;
    }
    
    // Try to extract business name patterns (look for capitalized words or brands)
    final words = text.split(RegExp(r'\s+'));
    
    // Look for common business name patterns
    for (int i = 0; i < words.length; i++) {
      final word = words[i].toLowerCase();
      
      // Skip common transaction words
      if (['spent', 'paid', 'buy', 'bought', 'purchase', 'at', 'in', 'from', 'for', 'on', 'dinner', 'lunch', 'breakfast'].contains(word)) {
        continue;
      }
      
      // Try to build a business name from this position
      final businessWords = <String>[];
      for (int j = i; j < words.length && businessWords.length < 4; j++) {
        final currentWord = words[j].toLowerCase();
        
        // Stop at prepositions that usually end business names, but continue for key business words
        if (['for', 'on', 'with', 'and'].contains(currentWord) && businessWords.isNotEmpty) {
          break;
        }
        
        // Skip common suffixes if they're not core to the business name
        if (['restaurant', 'coffee', 'shop', 'store', 'market', 'cafe'].contains(currentWord) && businessWords.length >= 2) {
          break;
        }
        
        businessWords.add(currentWord);
      }
      
      if (businessWords.isNotEmpty) {
        final businessName = businessWords.join(' ');
        if (businessName.length > 2 && businessName.length < 30) {
          return businessName;
        }
      }
    }
    
    return null;
  }

  /// Check if two texts match partially using word-level comparison
  bool _isPartialMatch(String text1, String text2) {
    final words1 = text1.split(' ').where((w) => w.isNotEmpty).toSet();
    final words2 = text2.split(' ').where((w) => w.isNotEmpty).toSet();
    
    // If either text has common words with the other, consider it a match
    if (words1.intersection(words2).isNotEmpty) {
      return true;
    }
    
    // Also check substring matches for shorter patterns
    if (text1.length < text2.length && text2.contains(text1)) {
      return true;
    }
    if (text2.length < text1.length && text1.contains(text2)) {
      return true;
    }
    
    return false;
  }

  /// Migration helper: Get all learned data for migration to LearnedAssociationService
  ///
  /// This method is used by LearnedAssociationService to migrate existing data
  /// from the legacy storage format to the new unified format.
  ///
  /// @deprecated This method is only for migration purposes and will be removed
  /// when LearnedCategoryStorage is fully deprecated.
  @Deprecated('Only for migration to LearnedAssociationService')
  Future<Map<String, String>> getAllLearnedCategoriesForMigration() async {
    try {
      final storedData = _storageService.getString(_storageKey);
      if (storedData == null) return {};

      final Map<String, dynamic> learnedMap = jsonDecode(storedData);
      return learnedMap.cast<String, String>();
    } catch (e) {
      print('Error getting learned categories for migration: $e');
      return {};
    }
  }

  /// Migration helper: Clear legacy data after successful migration
  ///
  /// This method should only be called by LearnedAssociationService after
  /// successful migration of all data.
  ///
  /// @deprecated This method is only for migration purposes and will be removed
  /// when LearnedCategoryStorage is fully deprecated.
  @Deprecated('Only for migration to LearnedAssociationService')
  Future<void> clearLegacyDataAfterMigration() async {
    try {
      await _storageService.remove(_storageKey);
    } catch (e) {
      print('Error clearing legacy data after migration: $e');
    }
  }
}
</file>

<file path="services/parser/mlkit_parser_service.dart">
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../models/amount_candidate.dart';
import '../../utils/currency_utils.dart';
import '../../utils/amount_utils.dart';
import '../../utils/raw_number_finder.dart';
import '../storage_service.dart';
import 'category_finder_service.dart';
import 'fallback_parser_service.dart';
import 'learned_association_service.dart';
import 'entity_extractor_base.dart';
import 'real_entity_extractor.dart';

/// Main orchestrator service that coordinates the entire parsing pipeline using ML Kit with fallback
class MlKitParserService {
  static MlKitParserService? _instance;
  static const String _modelDownloadedKey = 'ml_kit_model_downloaded';

  EntityExtractorBase? _entityExtractor;
  late CategoryFinderService _categoryFinder;
  late FallbackParserService _fallbackParser;
  late StorageService _storageService;
  late LearnedAssociationService _learnedAssociationService;
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;
  bool _mlKitAvailable = false;
  bool _isReady = false;

  MlKitParserService._();

  /// Factory constructor to get singleton instance
  /// [entityExtractor] - Optional entity extractor for dependency injection (mainly for testing)
  static Future<MlKitParserService> getInstance(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
  }) async {
    if (_instance == null) {
      _instance = MlKitParserService._();
      _instance!._storageService = storageService;
      _instance!._categoryFinder = CategoryFinderService(storageService);
      _instance!._fallbackParser = FallbackParserService(storageService);
      _instance!._learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
      await _instance!._initialize(entityExtractor);
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    _instance?._entityExtractor?.close();
    _instance = null;
  }

  /// Initialize ML Kit service in background after UI is shown
  static Future<MlKitParserService> initializeInBackground(StorageService storageService) async {
    if (_instance == null) {
      _instance = MlKitParserService._();
      _instance!._storageService = storageService;
      _instance!._categoryFinder = CategoryFinderService(storageService);
      _instance!._fallbackParser = FallbackParserService(storageService);
      _instance!._learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
      await _instance!._initializeWithCaching();
    }
    return _instance!;
  }

  /// Check if the service is fully ready for use
  bool get isReady => _isReady;

  /// Check if ML Kit is available (may be false if initialization failed)
  bool get isMlKitAvailable => _mlKitAvailable;

  /// Initialize ML Kit models with caching and fallback support
  Future<void> _initializeWithCaching([EntityExtractorBase? entityExtractor]) async {
    if (_isInitialized) return;

    // If an entity extractor was injected, use it instead of creating a real one
    if (entityExtractor != null) {
      _entityExtractor = entityExtractor;
      _mlKitAvailable = entityExtractor.isInitialized;
      _isReady = true;
      debugPrint('Using injected entity extractor');
      _isInitialized = true;
      return;
    }

    try {
      debugPrint('Attempting to initialize ML Kit with caching...');

      // Check if model was previously downloaded
      final wasModelDownloaded = _storageService.getBool(_modelDownloadedKey) ?? false;

      // Create and initialize the real entity extractor
      final realExtractor = RealEntityExtractor();
      await realExtractor.initializeWithCaching(wasModelDownloaded);

      // Mark model as downloaded for future launches
      if (!wasModelDownloaded) {
        await _storageService.setBool(_modelDownloadedKey, true);
        debugPrint('ML Kit model download status cached');
      }

      _entityExtractor = realExtractor;
      _mlKitAvailable = true;
      _isReady = true;
      debugPrint('ML Kit initialized successfully with caching');

    } catch (e) {
      debugPrint('Error initializing ML Kit: $e');
      debugPrint('Falling back to regex-based parsing');
      _mlKitAvailable = false;
      _isReady = true; // Still ready, just without ML Kit
      // ML Kit will be null, will use fallback parser
    }

    _isInitialized = true;
  }

  /// Initialize ML Kit models with fallback support (legacy method for testing)
  /// [entityExtractor] - Optional injected entity extractor (for testing)
  Future<void> _initialize([EntityExtractorBase? entityExtractor]) async {
    await _initializeWithCaching(entityExtractor);
  }

  /// Main entry point for parsing transactions
  Future<ParseResult> parseTransaction(String text) async {
    print('MLKIT_DEBUG: parseTransaction ENTRY POINT called with: "$text"');
    try {
      if (!_isInitialized) {
        await _initialize();
      }

      // NEW: Check learned associations first
      final learnedAssociation = await _learnedAssociationService.getAssociation(text);
      if (learnedAssociation != null) {
        final transaction = await _buildTransactionFromAssociation(text, learnedAssociation);
        return ParseResult.success(transaction);
      }

      // Always try ML Kit parsing logic first (even if entity extraction is not available)
      // This ensures we get multiple number detection and other advanced features
      print('MLKIT_DEBUG: Using ML Kit parsing logic (entity extraction available: ${_mlKitAvailable && _entityExtractor != null})');
      try {
        final mlResult = await _parseWithMLKit(text);
        // If ML Kit parsing triggers amount confirmation, return immediately
        if (mlResult.status == ParseStatus.needsAmountConfirmation) {
          return mlResult;
        }
        // If ML Kit succeeds or requires other input, return the result
        if (mlResult.status != ParseStatus.failed) {
          return mlResult;
        }
        // If ML Kit parsing logic fails, fall through to simple fallback
        print('MLKIT_DEBUG: ML Kit parsing logic failed, falling back to simple regex...');
      } catch (e) {
        print('MLKIT_DEBUG: ML Kit parsing logic failed with error, falling back to simple regex: $e');
        // Fall through to regex fallback
      }

      // Use fallback parser (regex-based) only as last resort
      print('MLKIT_DEBUG: Using simple fallback parser for text: "$text"');
      return await _fallbackParser.parseTransaction(text);

    } catch (e) {
      return ParseResult.failed(
        _createFallbackTransaction(text),
        'Parsing failed: $e'
      );
    }
  }

  /// Parse using ML Kit with Trust but Verify approach
  /// Implements the PRD's 4-step approach:
  /// 1. Call ML Kit service for money entities
  /// 2. Independently run raw number finder on original text
  /// 3. Consolidate both lists into comprehensive candidates
  /// 4. Apply ambiguity detection to consolidated list
  Future<ParseResult> _parseWithMLKit(String text) async {
    print('MLKIT_DEBUG: _parseWithMLKit called with text: "$text"');

    // Step 1: Extract entities using ML Kit (if available)
    List<EntityAnnotationBase> entities = [];
    if (_mlKitAvailable && _entityExtractor != null) {
      try {
        entities = await _entityExtractor!.annotateText(text);
        print('MLKIT_DEBUG: ML Kit found ${entities.length} entities: ${entities.map((e) => '${e.entityType}:${e.text}').toList()}');
      } catch (e) {
        print('MLKIT_DEBUG: ML Kit extraction failed: $e, continuing with raw number finder only');
        // Continue with empty entities list - we'll still use raw number finder
      }
    } else {
      print('MLKIT_DEBUG: ML Kit entity extraction not available, using raw number finder only');
    }

    // Step 1: Convert ML Kit entities to AmountCandidate objects
    final List<AmountCandidate> mlKitCandidates = [];
    DateTime? extractedDate;
    String remainingText = text;

    for (final entity in entities) {
      if (entity.entityType == EntityType.money) {
        debugPrint('DEBUG: Processing ML Kit money entity: "${entity.text}"');

        // Parse the money entity (without filtering embedded numbers)
        final result = _parseMoneyEntityToCandidate(entity, text);
        if (result != null) {
          mlKitCandidates.add(result);
          debugPrint('DEBUG: Added ML Kit candidate: ${result.amount} (${result.currency})');
        } else {
          debugPrint('DEBUG: Failed to parse ML Kit money entity: "${entity.text}"');
        }
        remainingText = _removeEntityFromText(remainingText, entity);
      } else if (entity.entityType == EntityType.dateTime) {
        extractedDate = _parseDateTimeEntity(entity);
        remainingText = _removeEntityFromText(remainingText, entity);
        debugPrint('DEBUG: Extracted date: $extractedDate');
      }
    }

    debugPrint('DEBUG: ML Kit candidates: ${mlKitCandidates.map((c) => c.amount).toList()}');

    // Step 2: Independently run raw number finder on original text
    final List<AmountCandidate> rawCandidates = RawNumberFinder.findAllNumbers(text);
    print('MLKIT_DEBUG: Raw finder found ${rawCandidates.length} candidates: ${rawCandidates.map((c) => c.amount).toList()}');

    // Step 3: Consolidate both lists into comprehensive candidates
    final List<AmountCandidate> consolidatedCandidates = _consolidateCandidates(mlKitCandidates, rawCandidates);
    print('MLKIT_DEBUG: Consolidated candidates: ${consolidatedCandidates.map((c) => c.amount).toList()}');

    // Step 4: Apply ambiguity detection to consolidated list
    final ambiguityResult = _detectAmountAmbiguityFromCandidates(consolidatedCandidates, text);
    if (ambiguityResult != null) {
      print('MLKIT_DEBUG: Ambiguity detected, triggering amount confirmation');
      return ambiguityResult;
    }

    print('MLKIT_DEBUG: No ambiguity detected, proceeding with best candidate selection');

    // If no ambiguity detected, select the best candidate
    final selectedCandidate = _selectBestAmountFromCandidates(consolidatedCandidates, text);
    if (selectedCandidate == null) {
      debugPrint('No valid amount candidate found, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    double finalAmount = selectedCandidate.amount;
    String? finalCurrency = selectedCandidate.currency;

    // If no currency found, get default currency
    if (finalCurrency == null) {
      finalCurrency = await _storageService.getDefaultCurrency();
    }

    // Detect transaction type with negative amount information
    final isNegativeAmount = text.trim().startsWith('-');
    final type = _detectTransactionType(text, isNegativeAmount: isNegativeAmount);

    // If transaction type is unclear, return needsType status
    if (type == null) {
      // Create partial transaction with default expense type for type disambiguation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: finalAmount,
        type: TransactionType.expense, // Default type, will be updated by user selection
        categoryId: 'other',
        date: extractedDate ?? DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: finalCurrency,
      );

      return ParseResult.needsType(partialTransaction);
    }

    // Find category using the category finder service
    final categoryId = await _categoryFinder.findCategory(remainingText, type);

    // Create the transaction - use 'unknown' placeholder when no category found
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: finalAmount,
      type: type,
      categoryId: categoryId ?? 'unknown', // Use placeholder for unknown categories
      date: extractedDate ?? DateTime.now(),
      description: _createDescription(text),
      tags: _extractTags(text),
      currencyCode: finalCurrency,
    );

    // Return result indicating if category selection is needed
    if (categoryId == null) {
      return ParseResult.needsCategory(transaction);
    } else {
      return ParseResult.success(transaction);
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    await _learnedAssociationService.learn(text, categoryId: categoryId);
  }

  /// Complete transaction parsing with user-confirmed amount
  Future<ParseResult> completeTransaction(String originalText, double confirmedAmount) async {
    debugPrint('DEBUG: completeTransaction called with text: "$originalText", amount: $confirmedAmount');

    try {
      // Learn the confirmed amount association
      await _learnedAssociationService.learn(originalText, confirmedAmount: confirmedAmount);
      debugPrint('DEBUG: Learned amount association for future use');

      // Determine transaction type
      final type = _detectTransactionType(originalText) ?? TransactionType.expense;
      debugPrint('DEBUG: Detected transaction type: $type');

      // Find category
      final categoryId = await _categoryFinder.findCategory(originalText, type);
      debugPrint('DEBUG: Found category: $categoryId');

      // Extract currency from original text
      final currency = _extractCurrencyFromText(originalText) ?? await _storageService.getDefaultCurrency();
      debugPrint('DEBUG: Using currency: $currency');

      // Create the final transaction
      final transaction = Transaction(
        id: _uuid.v4(),
        amount: confirmedAmount,
        type: type,
        categoryId: categoryId ?? 'unknown',
        date: DateTime.now(),
        description: originalText.trim(),
        tags: _extractTags(originalText),
        currencyCode: currency,
      );

      // Return result indicating if category selection is needed
      if (categoryId == null) {
        debugPrint('DEBUG: Transaction completed but needs category selection');
        return ParseResult.needsCategory(transaction);
      } else {
        debugPrint('DEBUG: Transaction completed successfully with amount: $confirmedAmount');
        return ParseResult.success(transaction);
      }
    } catch (e) {
      debugPrint('ERROR: Failed to complete transaction: $e');
      return ParseResult.failed(
        _createFallbackTransaction(originalText),
        'Failed to complete transaction: $e'
      );
    }
  }







  /// Check if the detected amount appears within an alphabetic sequence like "Lux68" or "Restaurant123"
  /// Uses entity start/end positions for accurate detection instead of unreliable indexOf searches
  bool _isEmbeddedInVendorName(String fullText, int start, int end) {
    // Validate positions
    if (start < 0 || end > fullText.length || start >= end) return false;

    // Check if the number has letters immediately before it (vendor name pattern)
    final beforeIndex = start - 1;
    final afterIndex = end;

    final hasLetterBefore = beforeIndex >= 0 &&
        RegExp(r'[A-Za-z]').hasMatch(fullText[beforeIndex]);
    final hasLetterAfter = afterIndex < fullText.length &&
        RegExp(r'[A-Za-z]').hasMatch(fullText[afterIndex]);

    // Consider it embedded if:
    // 1. Letters both before and after (like "Lux68City")
    // 2. Letters before and followed by space/end (like "Restaurant123 ")
    // 3. Multiple consecutive letters before (like "Restaurant123")
    if (hasLetterBefore && hasLetterAfter) {
      return true; // Classic embedded case like "Lux68City"
    }

    if (hasLetterBefore) {
      // Check if there are multiple letters before (indicating a vendor name)
      int letterCount = 0;
      for (int i = beforeIndex; i >= 0 && RegExp(r'[A-Za-z]').hasMatch(fullText[i]); i--) {
        letterCount++;
      }
      // If 3+ letters before the number, likely a vendor name
      return letterCount >= 3;
    }

    return false;
  }







  /// Format amount for display in confirmation UI
  String _formatAmountForDisplay(double amount) {
    if (amount >= 1000000000) {
      final billions = amount / 1000000000;
      return billions == billions.toInt() ? '${billions.toInt()}b' : '${billions.toStringAsFixed(1)}b';
    } else if (amount >= 1000000) {
      final millions = amount / 1000000;
      return millions == millions.toInt() ? '${millions.toInt()}m' : '${millions.toStringAsFixed(1)}m';
    } else if (amount >= 1000) {
      final thousands = amount / 1000;
      return thousands == thousands.toInt() ? '${thousands.toInt()}k' : '${thousands.toStringAsFixed(1)}k';
    } else {
      return amount == amount.toInt() ? amount.toInt().toString() : amount.toString();
    }
  }

  /// Check if the full text contains abbreviation patterns
  bool _hasAbbreviation(String text) {
    return RegExp(r'\d+[kKmMbB]\b').hasMatch(text);
  }



  /// Parse datetime entity from ML Kit
  DateTime? _parseDateTimeEntity(EntityAnnotationBase entity) {
    try {
      // For now, we'll use the current date as ML Kit entity extraction
      // doesn't directly provide parsed DateTime objects in this version
      return DateTime.now();
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Remove entity text from the remaining text
  String _removeEntityFromText(String text, EntityAnnotationBase entity) {
    final start = entity.start;
    final end = entity.end;
    
    if (start >= 0 && end <= text.length && end > start) {
      return (text.substring(0, start) + text.substring(end)).trim();
    }
    return text;
  }



  /// Extract currency information from text
  String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }



  /// Detect transaction type (from original parser)
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    final normalizedText = text.toLowerCase().trim();

    if (RegExp(r'^\s*-').hasMatch(normalizedText) || isNegativeAmount) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)')
        .hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
        .hasMatch(normalizedText)) {
      return TransactionType.income;
    }
    
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
        .hasMatch(normalizedText)) {
      return TransactionType.loan;
    }
    
    if (RegExp(r'\bfor\b').hasMatch(normalizedText) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'[$€£¥]').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Transaction _createFallbackTransaction(String text, {double? amount}) {
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: 'USD',
    );
  }

  /// Build transaction from learned association
  Future<Transaction> _buildTransactionFromAssociation(String text, LearnedAssociation association) async {
    // Use confirmed amount from association if available, otherwise extract from text
    double amount = 0.0;

    // Get default currency from storage instead of hardcoding USD
    final defaultCurrency = await _storageService.getDefaultCurrency();

    // Prioritize confirmed amount from learned association
    if (association.confirmedAmount != null) {
      amount = association.confirmedAmount!;
    } else {
      // Enhanced amount extraction for learned associations with abbreviation support
      final amountResult = AmountUtils.extractAmountFromText(text);
      if (amountResult != null) {
        amount = amountResult['amount'] as double;
      }
    }

    // Extract currency from text, fallback to default currency
    String currencyCode = _extractCurrencyFromText(text) ?? defaultCurrency;

    return Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: association.type ?? TransactionType.expense,
      categoryId: association.categoryId ?? 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: currencyCode,
    );
  }

  /// Parse money entity from ML Kit and convert to AmountCandidate
  /// This method does NOT filter out embedded numbers - that's handled later
  AmountCandidate? _parseMoneyEntityToCandidate(EntityAnnotationBase entity, String fullText) {
    try {
      // Extract the numeric value and currency from the money entity text
      final entityText = entity.text;

      // Enhanced regex pattern to include abbreviations: k/K, m/M, b/B
      final numericRegex = RegExp(r'(\d+(?:,\d{3})*(?:\.\d+)?[kKmMbB]?|\d+\.\d+[kKmMbB]?)');
      final match = numericRegex.firstMatch(entityText);
      double? amount;
      String? currency;

      if (match != null) {
        final numericString = match.group(1)!.replaceAll(',', '');

        // Use AmountUtils.parseAbbreviatedNumber() when abbreviation suffixes are detected
        if (numericString.toLowerCase().contains(RegExp(r'[kmb]$'))) {
          amount = AmountUtils.parseAbbreviatedNumber(numericString);
        } else {
          amount = double.tryParse(numericString);
        }
      }

      // Try to extract currency from the text with context
      currency = _extractCurrencyFromText(entityText);

      if (amount != null) {
        return AmountCandidate.fromMLKit(
          amount: amount,
          currency: currency,
          start: entity.start,
          end: entity.end,
          sourceText: entityText,
        );
      }
    } catch (e) {
      // Fallback to text parsing - handle thousands separators properly
      final cleanText = entity.text.replaceAll(RegExp(r'[^\d.,]'), '');
      // Remove thousands separators (commas) but preserve decimal point
      final normalizedText = cleanText.replaceAll(',', '');
      final amount = double.tryParse(normalizedText);
      if (amount != null) {
        return AmountCandidate.fromMLKit(
          amount: amount,
          currency: _extractCurrencyFromText(entity.text),
          start: entity.start,
          end: entity.end,
          sourceText: entity.text,
        );
      }
    }
    return null;
  }

  /// Consolidate ML Kit and raw number finder candidates
  /// Removes duplicates and merges the lists
  List<AmountCandidate> _consolidateCandidates(
    List<AmountCandidate> mlKitCandidates,
    List<AmountCandidate> rawCandidates,
  ) {
    debugPrint('DEBUG: _consolidateCandidates called');
    debugPrint('DEBUG: ML Kit candidates: ${mlKitCandidates.map((c) => c.amount).toList()}');
    debugPrint('DEBUG: Raw candidates: ${rawCandidates.map((c) => c.amount).toList()}');

    final List<AmountCandidate> consolidated = [];

    // Add all ML Kit candidates first
    consolidated.addAll(mlKitCandidates);
    debugPrint('DEBUG: Added ${mlKitCandidates.length} ML Kit candidates to consolidated list');

    // Add raw candidates that don't duplicate ML Kit candidates
    for (final rawCandidate in rawCandidates) {
      bool isDuplicate = false;

      for (final mlKitCandidate in mlKitCandidates) {
        // Check if they represent the same amount and position
        if (mlKitCandidate == rawCandidate) {
          isDuplicate = true;
          debugPrint('DEBUG: Raw candidate ${rawCandidate.amount} is duplicate of ML Kit candidate');
          break;
        }
      }

      if (!isDuplicate) {
        consolidated.add(rawCandidate);
        debugPrint('DEBUG: Added unique raw candidate: ${rawCandidate.amount}');
      }
    }

    // Sort by position for consistent ordering
    consolidated.sort((a, b) => a.start.compareTo(b.start));

    debugPrint('DEBUG: Final consolidated list: ${consolidated.map((c) => c.amount).toList()}');
    return consolidated;
  }

  /// Apply ambiguity detection to consolidated candidates
  ParseResult? _detectAmountAmbiguityFromCandidates(
    List<AmountCandidate> candidates,
    String text,
  ) {
    print('MLKIT_DEBUG: _detectAmountAmbiguityFromCandidates called with ${candidates.length} candidates');
    print('MLKIT_DEBUG: Total candidates: ${candidates.map((c) => c.amount).toList()}');

    if (candidates.length <= 1) {
      debugPrint('DEBUG: No ambiguity - only ${candidates.length} candidate(s)');
      return null; // No ambiguity with 0 or 1 candidates
    }

    // Filter candidates into embedded and non-embedded
    final nonEmbeddedCandidates = candidates.where((candidate) =>
        !_isEmbeddedInVendorName(text, candidate.start, candidate.end)).toList();
    final embeddedCandidates = candidates.where((candidate) =>
        _isEmbeddedInVendorName(text, candidate.start, candidate.end)).toList();

    print('MLKIT_DEBUG: Non-embedded candidates: ${nonEmbeddedCandidates.map((c) => c.amount).toList()}');
    print('MLKIT_DEBUG: Embedded candidates: ${embeddedCandidates.map((c) => c.amount).toList()}');

    List<AmountCandidate> candidatesForAmbiguity = [];

    // PRD v1.1.4 "Trust but Verify" approach: Include both embedded and non-embedded
    // when we have candidates from both categories, as this indicates genuine ambiguity
    if (nonEmbeddedCandidates.isNotEmpty && embeddedCandidates.isNotEmpty) {
      // Mixed scenario: both embedded (like "lux69") and standalone (like "100")
      candidatesForAmbiguity.addAll(embeddedCandidates);
      candidatesForAmbiguity.addAll(nonEmbeddedCandidates);
      print('MLKIT_DEBUG: Mixed scenario detected - including both embedded and non-embedded candidates');
    } else if (nonEmbeddedCandidates.length > 1) {
      // Multiple non-embedded candidates
      candidatesForAmbiguity.addAll(nonEmbeddedCandidates);
      debugPrint('DEBUG: Multiple non-embedded candidates detected');
    } else if (embeddedCandidates.length > 1) {
      // Multiple embedded candidates (less common but possible)
      candidatesForAmbiguity.addAll(embeddedCandidates);
      debugPrint('DEBUG: Multiple embedded candidates detected');
    } else {
      // Single candidate from each category or only one total - no ambiguity
      debugPrint('DEBUG: No ambiguity - insufficient candidates for confirmation');
      return null;
    }

    // Remove duplicates based on amount
    final uniqueCandidates = <AmountCandidate>[];
    for (final candidate in candidatesForAmbiguity) {
      bool isDuplicate = false;
      for (final existing in uniqueCandidates) {
        if (existing.hasSameAmount(candidate)) {
          isDuplicate = true;
          debugPrint('DEBUG: Duplicate amount found: ${candidate.amount} (skipping)');
          break;
        }
      }
      if (!isDuplicate) {
        uniqueCandidates.add(candidate);
        debugPrint('DEBUG: Added unique candidate: ${candidate.amount}');
      }
    }

    debugPrint('DEBUG: Unique candidates after deduplication: ${uniqueCandidates.map((c) => c.amount).toList()}');

    // Trigger ambiguity if we have multiple unique candidates
    if (uniqueCandidates.length > 1) {
      print('MLKIT_DEBUG: Triggering amount confirmation for ${uniqueCandidates.length} candidates');
      print('MLKIT_DEBUG: Final decision: AMOUNT CONFIRMATION NEEDED');

      final amounts = uniqueCandidates.map((c) => c.amount).toList();
      final texts = uniqueCandidates.map((c) => _formatAmountForDisplay(c.amount)).toList();

      // Get currency from first candidate or use default
      final currency = uniqueCandidates.first.currency ?? 'USD';

      // Create partial transaction for confirmation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: amounts.first, // Temporary amount
        type: _detectTransactionType(text) ?? TransactionType.expense,
        categoryId: 'unknown',
        date: DateTime.now(),
        description: text.trim(),
        tags: _extractTags(text),
        currencyCode: currency,
      );

      return ParseResult.needsAmountConfirmation(
        partialTransaction,
        amounts,
        texts,
      );
    }

    debugPrint('DEBUG: Final decision: NO AMBIGUITY DETECTED');
    return null; // No ambiguity detected
  }

  /// Select the best amount candidate from consolidated list
  AmountCandidate? _selectBestAmountFromCandidates(
    List<AmountCandidate> candidates,
    String text,
  ) {
    if (candidates.isEmpty) return null;
    if (candidates.length == 1) return candidates.first;

    // Prefer non-embedded candidates
    final nonEmbeddedCandidates = candidates.where((candidate) =>
        !_isEmbeddedInVendorName(text, candidate.start, candidate.end)).toList();

    if (nonEmbeddedCandidates.isNotEmpty) {
      // Among non-embedded candidates, prefer those with abbreviations
      final abbreviatedCandidates = nonEmbeddedCandidates.where((candidate) =>
          _hasAbbreviation(candidate.sourceText)).toList();

      if (abbreviatedCandidates.isNotEmpty) {
        return abbreviatedCandidates.first;
      }

      return nonEmbeddedCandidates.first;
    }

    // Fall back to first candidate if all are embedded
    return candidates.first;
  }

  /// Dispose resources
  void dispose() {
    _entityExtractor?.close();
  }
}
</file>

<file path="services/parser/real_entity_extractor.dart">
import 'package:google_mlkit_entity_extraction/google_mlkit_entity_extraction.dart' as mlkit;
import 'entity_extractor_base.dart';

/// Concrete implementation of EntityAnnotationBase that wraps ML Kit EntityAnnotation
class RealEntityAnnotation implements EntityAnnotationBase {
  final mlkit.EntityAnnotation _mlkitAnnotation;

  RealEntityAnnotation(this._mlkitAnnotation);

  @override
  String get text => _mlkitAnnotation.text;

  @override
  int get start => _mlkitAnnotation.start;

  @override
  int get end => _mlkitAnnotation.end;

  @override
  EntityType get entityType {
    // Convert ML Kit entity type to our abstraction
    // Using runtime type checking as the current implementation does
    final runtimeType = _mlkitAnnotation.runtimeType.toString();
    if (runtimeType.contains('Money')) {
      return EntityType.money;
    } else if (runtimeType.contains('DateTime')) {
      return EntityType.dateTime;
    } else if (runtimeType.contains('Address')) {
      return EntityType.address;
    } else if (runtimeType.contains('Email')) {
      return EntityType.email;
    } else if (runtimeType.contains('Phone')) {
      return EntityType.phone;
    } else if (runtimeType.contains('Url')) {
      return EntityType.url;
    } else {
      return EntityType.other;
    }
  }

  /// Get the original ML Kit annotation for cases where specific ML Kit functionality is needed
  mlkit.EntityAnnotation get mlkitAnnotation => _mlkitAnnotation;
}

/// Concrete implementation of EntityExtractorBase that wraps the actual Google ML Kit EntityExtractor
class RealEntityExtractor implements EntityExtractorBase {
  mlkit.EntityExtractor? _entityExtractor;
  bool _isInitialized = false;

  /// Initialize the ML Kit entity extractor
  /// This handles model downloading and initialization
  Future<void> initialize() async {
    await initializeWithCaching(false);
  }

  /// Initialize the ML Kit entity extractor with caching optimization
  /// [modelWasDownloaded] - Whether the model was previously downloaded (to skip check)
  Future<void> initializeWithCaching(bool modelWasDownloaded) async {
    if (_isInitialized) return;

    try {
      print('Initializing Real ML Kit Entity Extractor with caching...');

      // Skip model download check if we know it was already downloaded
      if (!modelWasDownloaded) {
        // Check if model is available, download if needed
        final manager = mlkit.EntityExtractorModelManager();
        const languageTag = 'en'; // Use language tag instead of enum
        final isDownloaded = await manager.isModelDownloaded(languageTag);

        if (!isDownloaded) {
          print('Downloading ML Kit model...');
          await manager.downloadModel(languageTag);
          print('ML Kit model downloaded successfully');
        }
      } else {
        print('Skipping model download check (cached as downloaded)');
      }

      _entityExtractor = mlkit.EntityExtractor(language: mlkit.EntityExtractorLanguage.english);
      _isInitialized = true;
      print('Real ML Kit Entity Extractor initialized successfully');
    } catch (e) {
      print('Failed to initialize Real ML Kit Entity Extractor: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  @override
  Future<List<EntityAnnotationBase>> annotateText(String text) async {
    if (!_isInitialized || _entityExtractor == null) {
      await initialize();
    }

    if (_entityExtractor == null) {
      throw Exception('ML Kit Entity Extractor not initialized');
    }

    try {
      final entities = await _entityExtractor!.annotateText(text);
      return entities.map((entity) => RealEntityAnnotation(entity)).toList();
    } catch (e) {
      print('Error during ML Kit entity annotation: $e');
      rethrow;
    }
  }

  @override
  Future<void> close() async {
    if (_entityExtractor != null) {
      await _entityExtractor!.close();
      _entityExtractor = null;
    }
    _isInitialized = false;
  }

  @override
  bool get isInitialized => _isInitialized && _entityExtractor != null;
}
</file>

<file path="services/localization_service.dart">
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/localization_data.dart';

/// Abstract interface for localization services
abstract class LocalizationServiceInterface {
  Future<LocalizationData> getPatternsForLocale(Locale locale);
  Future<void> preloadLocale(Locale locale);
  void clearCache();
  bool isLocaleCached(Locale locale);
  int get cacheSize;
  List<String> get availableLocales;
}

/// Service for loading and caching localization data for transaction parsing
class LocalizationService implements LocalizationServiceInterface {
  static LocalizationService? _instance;
  static LocalizationService get instance => _instance ??= LocalizationService._();

  LocalizationService._();

  /// Public constructor for testing
  @visibleForTesting
  LocalizationService.forTesting();

  // Cache for loaded localization data
  final Map<String, LocalizationData> _cache = {};

  // Default fallback locale
  static const String _defaultLocale = 'en';

  /// Gets localization patterns for the specified locale
  /// Falls back to English if the requested locale is not available
  Future<LocalizationData> getPatternsForLocale(Locale locale) async {
    final localeKey = _getLocaleKey(locale);
    
    // Check cache first
    if (_cache.containsKey(localeKey)) {
      return _cache[localeKey]!;
    }

    try {
      // Try to load the requested locale
      final data = await _loadLocaleData(localeKey);
      _cache[localeKey] = data;
      return data;
    } catch (e) {
      // If loading fails and it's not the default locale, try fallback
      if (localeKey != _defaultLocale) {
        try {
          final fallbackData = await _loadLocaleData(_defaultLocale);
          _cache[_defaultLocale] = fallbackData;
          return fallbackData;
        } catch (fallbackError) {
          throw Exception('Failed to load localization data for $localeKey and fallback $_defaultLocale: $fallbackError');
        }
      } else {
        throw Exception('Failed to load default localization data: $e');
      }
    }
  }

  /// Preloads localization data for the specified locale for performance optimization
  Future<void> preloadLocale(Locale locale) async {
    final localeKey = _getLocaleKey(locale);
    if (!_cache.containsKey(localeKey)) {
      try {
        await getPatternsForLocale(locale);
      } catch (e) {
        // Preloading failures are not critical, just log them
        print('Warning: Failed to preload locale $localeKey: $e');
      }
    }
  }

  /// Clears the localization cache to free memory
  void clearCache() {
    _cache.clear();
  }

  /// Gets the number of cached locales (for testing/debugging)
  int get cacheSize => _cache.length;

  /// Checks if a locale is cached
  bool isLocaleCached(Locale locale) {
    return _cache.containsKey(_getLocaleKey(locale));
  }

  /// Loads localization data from assets
  Future<LocalizationData> _loadLocaleData(String localeKey) async {
    final assetPath = 'assets/l10n/$localeKey.json';
    
    try {
      final jsonString = await rootBundle.loadString(assetPath);
      return LocalizationData.fromJsonString(jsonString);
    } on FlutterError catch (e) {
      if (e.message.contains('Unable to load asset')) {
        throw Exception('Localization file not found: $assetPath');
      }
      rethrow;
    } catch (e) {
      throw Exception('Failed to parse localization file $assetPath: $e');
    }
  }

  /// Converts a Locale to a cache key
  String _getLocaleKey(Locale locale) {
    // Use language code only for simplicity (e.g., 'en' instead of 'en-US')
    return locale.languageCode;
  }

  /// Gets available locales based on cached data
  List<String> get availableLocales => _cache.keys.toList();

  /// Validates that a LocalizationData object has all required fields
  static bool validateLocalizationData(LocalizationData data) {
    try {
      // Check that all required fields are present and non-empty
      if (data.locale.isEmpty) return false;
      if (data.decimalSeparator.isEmpty) return false;
      if (data.thousandsSeparator.isEmpty) return false;
      if (data.expenseKeywords.isEmpty) return false;
      if (data.incomeKeywords.isEmpty) return false;
      if (data.loanKeywords.isEmpty) return false;
      if (data.currencySymbols.isEmpty) return false;
      
      // Check that keywords don't contain empty strings
      if (data.expenseKeywords.any((k) => k.isEmpty)) return false;
      if (data.incomeKeywords.any((k) => k.isEmpty)) return false;
      if (data.loanKeywords.any((k) => k.isEmpty)) return false;
      if (data.currencySymbols.any((k) => k.isEmpty)) return false;
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Creates a test instance for unit testing
  static LocalizationService createTestInstance() {
    return LocalizationService._();
  }

  /// Resets the singleton instance (for testing)
  static void resetInstance() {
    _instance = null;
  }

  /// Sets a test instance as the singleton (for testing)
  static void setTestInstance(LocalizationService testInstance) {
    _instance = testInstance;
  }
}
</file>

<file path="services/startup_service.dart">
import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

import 'storage_service.dart';
import 'parser/mlkit_parser_service.dart';
import '../models/transaction_model.dart';
import '../theme.dart';

/// Enumeration of possible service initialization states
enum ServiceInitializationState {
  pending,
  loading,
  ready,
  failed,
}

/// Data class to track service initialization status
class ServiceStatus {
  final ServiceInitializationState state;
  final String? error;
  final DateTime? startTime;
  final DateTime? endTime;

  ServiceStatus({
    required this.state,
    this.error,
    this.startTime,
    this.endTime,
  });

  Duration? get duration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return null;
  }

  ServiceStatus copyWith({
    ServiceInitializationState? state,
    String? error,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    return ServiceStatus(
      state: state ?? this.state,
      error: error ?? this.error,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }
}

/// Singleton service that manages the progressive initialization of all app services
class StartupService extends ChangeNotifier {
  static StartupService? _instance;
  
  final StorageService _storageService;
  final Map<String, ServiceStatus> _serviceStatuses = {};
  final StreamController<Map<String, ServiceStatus>> _statusController = 
      StreamController<Map<String, ServiceStatus>>.broadcast();
  
  // Service instances
  MlKitParserService? _mlKitService;
  TransactionProvider? _transactionProvider;
  ThemeProvider? _themeProvider;
  
  // Configuration
  static const Duration _initializationTimeout = Duration(minutes: 2);
  static const int _maxRetryAttempts = 3;
  
  StartupService._(this._storageService) {
    _initializeServiceStatuses();
  }

  /// Get singleton instance
  static StartupService getInstance(StorageService storageService) {
    _instance ??= StartupService._(storageService);
    return _instance!;
  }

  /// Reset singleton instance (for testing)
  static void resetInstance() {
    _instance?._statusController.close();
    _instance = null;
  }

  // Getters for service instances
  MlKitParserService? get mlKitService => _mlKitService;
  TransactionProvider? get transactionProvider => _transactionProvider;
  ThemeProvider? get themeProvider => _themeProvider;

  // Status getters
  Map<String, ServiceStatus> get serviceStatuses => Map.unmodifiable(_serviceStatuses);
  Stream<Map<String, ServiceStatus>> get statusStream => _statusController.stream;
  
  bool get isAllServicesReady => _serviceStatuses.values
      .every((status) => status.state == ServiceInitializationState.ready);
  
  bool get hasAnyServiceFailed => _serviceStatuses.values
      .any((status) => status.state == ServiceInitializationState.failed);

  /// Initialize service status tracking
  void _initializeServiceStatuses() {
    _serviceStatuses['theme'] = ServiceStatus(state: ServiceInitializationState.pending);
    _serviceStatuses['transaction'] = ServiceStatus(state: ServiceInitializationState.pending);
    _serviceStatuses['mlkit'] = ServiceStatus(state: ServiceInitializationState.pending);
  }

  /// Initialize all services progressively
  Future<void> initializeAllServices() async {
    developer.log('Starting progressive service initialization', name: 'StartupService');
    
    try {
      // Initialize services in order of dependency and importance
      await _initializeWithRetry('theme', _initializeThemeProvider);
      await _initializeWithRetry('transaction', _initializeTransactionProvider);
      await _initializeWithRetry('mlkit', _initializeMlKitService);
      
      developer.log('All services initialized successfully', name: 'StartupService');
    } catch (e) {
      developer.log('Service initialization completed with errors: $e', name: 'StartupService');
    }
    
    notifyListeners();
  }

  /// Initialize a service with retry logic and timeout
  Future<void> _initializeWithRetry(String serviceName, Future<void> Function() initializer) async {
    int attempts = 0;
    
    while (attempts < _maxRetryAttempts) {
      attempts++;
      
      try {
        _updateServiceStatus(serviceName, ServiceInitializationState.loading, startTime: DateTime.now());
        
        await initializer().timeout(_initializationTimeout);
        
        _updateServiceStatus(serviceName, ServiceInitializationState.ready, endTime: DateTime.now());
        developer.log('$serviceName service initialized successfully (attempt $attempts)', name: 'StartupService');
        return;
        
      } catch (e) {
        final errorMessage = 'Failed to initialize $serviceName service (attempt $attempts): $e';
        developer.log(errorMessage, name: 'StartupService');
        
        if (attempts >= _maxRetryAttempts) {
          _updateServiceStatus(serviceName, ServiceInitializationState.failed, 
              error: errorMessage, endTime: DateTime.now());
          // Don't rethrow - allow other services to continue initializing
          return;
        }
        
        // Wait before retry
        await Future.delayed(Duration(milliseconds: 500 * attempts));
      }
    }
  }

  /// Initialize theme provider
  Future<void> _initializeThemeProvider() async {
    _themeProvider = ThemeProvider();
    await _themeProvider!.initialize();
  }

  /// Initialize transaction provider
  Future<void> _initializeTransactionProvider() async {
    _transactionProvider = TransactionProvider(_storageService);
    await _transactionProvider!.initialize();
  }

  /// Initialize ML Kit service in background
  Future<void> _initializeMlKitService() async {
    try {
      _mlKitService = await MlKitParserService.initializeInBackground(_storageService);
    } catch (e) {
      // ML Kit failure is not critical - app can work without it
      developer.log('ML Kit service initialization failed, app will use fallback parsing: $e', name: 'StartupService');
      rethrow;
    }
  }

  /// Update service status and notify listeners
  void _updateServiceStatus(String serviceName, ServiceInitializationState state, 
      {String? error, DateTime? startTime, DateTime? endTime}) {
    final currentStatus = _serviceStatuses[serviceName]!;
    _serviceStatuses[serviceName] = currentStatus.copyWith(
      state: state,
      error: error,
      startTime: startTime ?? currentStatus.startTime,
      endTime: endTime,
    );
    
    _statusController.add(Map.unmodifiable(_serviceStatuses));
    notifyListeners();
  }

  /// Check if a specific service is ready
  bool isServiceReady(String serviceName) {
    return _serviceStatuses[serviceName]?.state == ServiceInitializationState.ready;
  }

  /// Get initialization duration for a service
  Duration? getServiceInitializationDuration(String serviceName) {
    return _serviceStatuses[serviceName]?.duration;
  }

  /// Get total initialization time
  Duration? getTotalInitializationTime() {
    final durations = _serviceStatuses.values
        .map((status) => status.duration)
        .where((duration) => duration != null)
        .cast<Duration>();
    
    if (durations.isEmpty) return null;
    
    return durations.reduce((a, b) => a + b);
  }

  /// Retry failed service initialization
  Future<void> retryFailedServices() async {
    final failedServices = _serviceStatuses.entries
        .where((entry) => entry.value.state == ServiceInitializationState.failed)
        .map((entry) => entry.key)
        .toList();
    
    for (final serviceName in failedServices) {
      switch (serviceName) {
        case 'theme':
          await _initializeWithRetry('theme', _initializeThemeProvider);
          break;
        case 'transaction':
          await _initializeWithRetry('transaction', _initializeTransactionProvider);
          break;
        case 'mlkit':
          await _initializeWithRetry('mlkit', _initializeMlKitService);
          break;
      }
    }
  }

  @override
  void dispose() {
    _statusController.close();
    super.dispose();
  }
}
</file>

<file path="services/storage_service.dart">
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  late SharedPreferences _prefs;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // String operations
  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  String? getString(String key) {
    return _prefs.getString(key);
  }

  // String list operations
  Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  // Bool operations
  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  // Int operations
  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  // Double operations
  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  // Remove a specific key
  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  // Clear all data
  Future<bool> clear() async {
    return await _prefs.clear();
  }

  // Check if a key exists
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  // Default currency operations
  Future<void> saveDefaultCurrency(String currencyCode) async {
    await setString('default_currency', currencyCode);
  }

  Future<String> getDefaultCurrency() async {
    return getString('default_currency') ?? 'USD';
  }
}
</file>

<file path="services/transaction_parser_service.dart">
import 'package:uuid/uuid.dart';
import '../models/transaction_model.dart';
import '../utils/amount_utils.dart';

class TransactionParserService {
  final Uuid _uuid = Uuid();
  
  /// Parse transaction from text using regular expressions
  /// Returns null if no transaction could be parsed
  Transaction? parseTransaction(String text) {
    // Normalize text for easier parsing
    final normalizedText = text.toLowerCase().trim();
    
    // Try to detect transaction type
    TransactionType? detectedType = _detectTransactionType(normalizedText);
    if (detectedType == null) return null;
    
    // Try to extract amount
    double? amount = _extractAmount(normalizedText);
    if (amount == null) return null;
    
    // Try to detect category
    String categoryId = _detectCategory(normalizedText, detectedType);
    
    // Create description from the text
    String description = _createDescription(normalizedText);
    
    // Extract tags if any
    List<String> tags = _extractTags(normalizedText);
    
    // Create and return the transaction
    return Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: detectedType,
      categoryId: categoryId,
      date: DateTime.now(),
      description: description,
      tags: tags,
    );
  }
  
  /// Detect transaction type from text
  TransactionType? _detectTransactionType(String text) {
    // Check for negative sign or minus at the beginning which indicates expense
    if (RegExp(r'^\s*-').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Expense patterns
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought|bought for)')
        .hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Income patterns
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
        .hasMatch(text)) {
      return TransactionType.income;
    }
    
    // Loan patterns
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
        .hasMatch(text)) {
      return TransactionType.loan;
    }
    
    // Special case: 'for' keyword typically indicates expense unless preceded by income keyword
    if (RegExp(r'\bfor\b').hasMatch(text) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Default to expense if contains $ or other currency symbols
    if (RegExp(r'[$€£¥]').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    return null;
  }
  
  /// Extract amount from text
  double? _extractAmount(String text) {
    // Handle negative amount pattern first (like -500$ for toys)
    if (text.startsWith('-')) {
      // Remove the leading minus for easier parsing
      final trimmedText = text.substring(1).trim();
      final amount = _extractPositiveAmount(trimmedText);
      return amount; // It's already an expense based on detection type
    }
    
    // Regular amount extraction for positive values
    return _extractPositiveAmount(text);
  }
  
  /// Helper to extract positive amount values from text with abbreviation support
  double? _extractPositiveAmount(String text) {
    // Use AmountUtils for enhanced amount extraction with abbreviation support
    final result = AmountUtils.extractAmountFromText(text);

    if (result != null) {
      return result['amount'] as double;
    }

    // Fallback to original regex for backward compatibility
    final amountRegex = RegExp(r'\$?\s?(\d+[.,]?\d*)\s?(?:dollars|USD|\$)?');
    final match = amountRegex.firstMatch(text);

    if (match != null) {
      final amountStr = match.group(1)?.replaceAll(',', '.') ?? '';
      try {
        return double.parse(amountStr);
      } catch (e) {
        return null;
      }
    }

    return null;
  }
  
  /// Detect category based on keywords in text
  String _detectCategory(String text, TransactionType type) {
    // Expense categories
    if (type == TransactionType.expense) {
      if (_containsAny(text, ['food', 'meal', 'restaurant', 'lunch', 'dinner', 'breakfast', 'grocery', 'pizza', 'burger', 'cafe', 'coffee', 'snack'])) {
        return 'food';
      }
      if (_containsAny(text, ['transport', 'gas', 'fuel', 'uber', 'taxi', 'car', 'bus', 'train', 'transportation', 'fare', 'metro', 'subway', 'parking'])) {
        return 'transport';
      }
      if (_containsAny(text, ['shopping', 'clothes', 'shoes', 'dress', 'shirt', 'amazon', 'online', 'buy', 'purchase', 'mall', 'store', 'retail', 'toys', 'toy'])) {
        return 'shopping';
      }
      if (_containsAny(text, ['utilities', 'bill', 'electricity', 'water', 'gas bill', 'internet', 'phone', 'wifi', 'broadband', 'service', 'subscription'])) {
        return 'utilities';
      }
      if (_containsAny(text, ['entertainment', 'movie', 'cinema', 'concert', 'show', 'theater', 'game', 'subscription', 'netflix', 'spotify', 'music', 'streaming', 'app'])) {
        return 'entertainment';
      }
      if (_containsAny(text, ['health', 'doctor', 'medicine', 'hospital', 'medical', 'pharmacy', 'dentist', 'clinic', 'therapy', 'treatment', 'drug'])) {
        return 'health';
      }
      if (_containsAny(text, ['gift', 'present', 'donation', 'charity', 'tip', 'birthday', 'anniversary'])) {
        return 'gift';
      }
    }
    
    // Income categories
    if (type == TransactionType.income) {
      if (_containsAny(text, ['salary', 'wage', 'paycheck', 'payment', 'work', 'job', 'employment', 'employer', 'pay', 'compensation'])) {
        return 'salary';
      }
      if (_containsAny(text, ['gift', 'present', 'donation', 'birthday', 'holiday', 'christmas', 'received money'])) {
        return 'gift';
      }
      if (_containsAny(text, ['interest', 'dividend', 'investment', 'stock', 'return', 'capital gain', 'yield', 'shares', 'mutual fund', 'etf'])) {
        return 'investment';
      }
      if (_containsAny(text, ['bonus', 'commission', 'incentive', 'reward', 'performance', 'achievement'])) {
        return 'bonus';
      }
      if (_containsAny(text, ['selling', 'sold', 'sale', 'resale', 'marketplace', 'ebay', 'craigslist', 'facebook marketplace', 'garage sale'])) {
        return 'sales';
      }
      if (_containsAny(text, ['crypto', 'bitcoin', 'ethereum', 'cryptocurrency', 'token', 'blockchain', 'mining'])) {
        return 'crypto';
      }
      if (_containsAny(text, ['refund', 'reimbursement', 'cashback', 'return'])) {
        return 'refund';
      }
    }
    
    // Loan category
    if (type == TransactionType.loan) {
      return 'loan';
    }
    
    // Default categories based on transaction type
    switch (type) {
      case TransactionType.expense:
        return 'shopping';
      case TransactionType.income:
        return 'other_income';
      case TransactionType.loan:
        return 'loan';
    }
  }
  
  /// Check if text contains any of the words
  bool _containsAny(String text, List<String> words) {
    for (final word in words) {
      if (text.contains(word)) {
        return true;
      }
    }
    return false;
  }
  
  /// Create a meaningful description from the text
  String _createDescription(String text) {
    // Remove currency symbols and amounts
    final cleanedText = text
        .replaceAll(RegExp(r'\$?\s?\d+[.,]?\d*\s?(?:dollars|USD|\$)?'), '')
        .replaceAll(RegExp(r'spent|paid|bought|received|earned|borrowed|lent'), '')
        .trim();
    
    // If text is too short, return original text
    if (cleanedText.length < 5 && text.length > 5) {
      return text;
    }
    
    // Capitalize first letter
    if (cleanedText.isNotEmpty) {
      return cleanedText[0].toUpperCase() + cleanedText.substring(1);
    }
    
    return 'Transaction';
  }
  
  /// Extract tags from text (words with # prefix)
  List<String> _extractTags(String text) {
    final tagRegex = RegExp(r'#(\w+)');
    final matches = tagRegex.allMatches(text);
    
    if (matches.isNotEmpty) {
      return matches.map((match) => match.group(1) ?? '').where((tag) => tag.isNotEmpty).toList();
    }
    
    return [];
  }
}
</file>

<file path="utils/amount_utils.dart">
import 'currency_utils.dart';

/// Utility class for handling amount parsing with abbreviation support
/// Provides methods to parse abbreviated numbers (k/M/B) and extract amounts from text
class AmountUtils {
  
  /// Parse abbreviated number strings like '100k', '2.5M', '1.2B'
  /// Returns the expanded numeric value or null for invalid input
  /// 
  /// Supported abbreviations:
  /// - k/K: thousands (multiply by 1,000)
  /// - m/M: millions (multiply by 1,000,000)  
  /// - b/B: billions (multiply by 1,000,000,000)
  /// 
  /// Examples:
  /// - '100k' → 100000.0
  /// - '2.5M' → 2500000.0
  /// - '1.2B' → 1200000000.0
  /// - '0k' → 0.0
  /// - 'invalid' → null
  static double? parseAbbreviatedNumber(String token) {
    if (token.isEmpty) return null;
    
    final trimmed = token.trim();
    if (trimmed.isEmpty) return null;
    
    // Check if the token ends with an abbreviation
    final lastChar = trimmed[trimmed.length - 1].toLowerCase();
    double multiplier;
    String numberPart;
    
    switch (lastChar) {
      case 'k':
        multiplier = 1000.0;
        numberPart = trimmed.substring(0, trimmed.length - 1);
        break;
      case 'm':
        multiplier = 1000000.0;
        numberPart = trimmed.substring(0, trimmed.length - 1);
        break;
      case 'b':
        multiplier = 1000000000.0;
        numberPart = trimmed.substring(0, trimmed.length - 1);
        break;
      default:
        // No abbreviation, try to parse as regular number
        return double.tryParse(trimmed);
    }
    
    // Parse the numeric part
    final baseNumber = double.tryParse(numberPart);
    if (baseNumber == null) return null;
    
    // Handle edge cases
    if (baseNumber.isNaN || baseNumber.isInfinite) return null;
    
    final result = baseNumber * multiplier;
    
    // Check for overflow/underflow
    if (result.isInfinite || result.isNaN) return null;
    
    return result;
  }
  
  /// Extract amount and currency from text with abbreviation support
  /// Returns a map with 'amount' (double) and 'currency' (String?) keys
  /// 
  /// Supports:
  /// - Number abbreviations: '100k', '2.5M', '1.2B'
  /// - Currency symbols: '$', '€', '£', '¥', etc.
  /// - Currency codes: 'USD', 'EUR', 'GBP', etc.
  /// - Currency names: 'dollars', 'euros', 'pounds', etc.
  /// - Thousands separators: '1,500k', '2,500.50M'
  /// - Decimal separators: configurable via parameters
  /// 
  /// Parameters:
  /// - text: The text to parse
  /// - thousandsSeparator: Optional thousands separator (default: ',')
  /// - decimalSeparator: Optional decimal separator (default: '.')
  /// 
  /// Returns:
  /// - Map with 'amount' and 'currency' keys, or null if no amount found
  static Map<String, dynamic>? extractAmountFromText(
    String text, {
    String? thousandsSeparator,
    String? decimalSeparator,
  }) {
    if (text.isEmpty) return null;
    
    final thousandsSep = thousandsSeparator ?? ',';
    final decimalSep = decimalSeparator ?? '.';
    
    // Build currency symbols pattern
    final currencySymbolsPattern = r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)';
    
    // Build amount pattern with abbreviation support
    // Escape separators for regex
    final thousandsEscaped = RegExp.escape(thousandsSep);
    final decimalEscaped = RegExp.escape(decimalSep);
    
    // Pattern for numbers with optional thousands separators, decimals, and abbreviations
    final amountPattern = r'(\d+(?:' + thousandsEscaped + r'\d{3})*(?:' + decimalEscaped + r'\d+)?[kKmMbB]?)';
    
    // Combined regex pattern for currency symbol + amount or amount + currency
    final combinedPattern = currencySymbolsPattern + r'\s?' + amountPattern + 
                           r'|' + amountPattern + r'\s?(?:dollars?|USD|euros?|EUR|pounds?|GBP|yen|JPY|yuan|CNY|rupees?|INR|rubles?|RUB|won|KRW|pesos?|MXN|PHP|dong|VND|baht|THB|lira|TRY|shekel|ILS|reais?|BRL|SGD|HKD|AUD|CAD|NZD|' + currencySymbolsPattern + r')?';
    
    final regex = RegExp(combinedPattern, caseSensitive: false);
    final match = regex.firstMatch(text);
    
    if (match == null) return null;
    
    String? currencySymbol;
    String? amountString;
    
    // Check which pattern matched
    if (match.group(1) != null) {
      // Currency symbol + amount pattern
      currencySymbol = match.group(1);
      amountString = match.group(2);
    } else if (match.group(3) != null) {
      // Amount + optional currency pattern
      amountString = match.group(3);
      currencySymbol = match.group(4); // This might be null
    }
    
    if (amountString == null) return null;
    
    // Normalize the amount string by removing thousands separators
    String normalizedAmount = amountString.replaceAll(thousandsSep, '');
    
    // Replace decimal separator with standard dot if different
    if (decimalSep != '.') {
      normalizedAmount = normalizedAmount.replaceAll(decimalSep, '.');
    }
    
    // Parse the amount (with potential abbreviation)
    final amount = parseAbbreviatedNumber(normalizedAmount);
    if (amount == null) return null;
    
    // Extract currency information
    String? currency = _extractCurrencyFromText(text);
    if (currency == null && currencySymbol != null) {
      currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
    }
    
    return {
      'amount': amount,
      'currency': currency,
    };
  }
  
  /// Extract currency information from text
  /// Returns currency code or null if not found
  static String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }
}
</file>

<file path="utils/currency_utils.dart">
import 'package:intl/intl.dart';

class CurrencyUtils {
  // Static map of currency codes to their symbols
  static const Map<String, String> currencySymbols = {
    'USD': '\$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CNY': '¥',
    'INR': '₹',
    'KRW': '₩',
    'AUD': 'A\$',
    'CAD': 'C\$',
    'CHF': 'CHF',
    'SEK': 'kr',
    'NOK': 'kr',
    'DKK': 'kr',
    'RUB': '₽',
    'BRL': 'R\$',
    'MXN': '\$',
    'ZAR': 'R',
    'SGD': 'S\$',
    'HKD': 'HK\$',
    'NZD': 'NZ\$',
    'THB': '฿',
    'TRY': '₺',
    'PLN': 'zł',
    'CZK': 'Kč',
    'HUF': 'Ft',
    'ILS': '₪',
    'AED': 'د.إ',
    'SAR': '﷼',
    'EGP': '£',
    'QAR': '﷼',
    'KWD': 'د.ك',
    'BHD': '.د.ب',
    'OMR': '﷼',
    'JOD': 'د.ا',
    'LBP': '£',
    'MAD': 'د.م.',
    'TND': 'د.ت',
    'DZD': 'د.ج',
    'LYD': 'ل.د',
    'SDG': 'ج.س.',
    'ETB': 'Br',
    'KES': 'KSh',
    'UGX': 'USh',
    'TZS': 'TSh',
    'GHS': '¢',
    'NGN': '₦',
    'XOF': 'CFA',
    'XAF': 'FCFA',
    'MWK': 'MK',
    'ZMW': 'ZK',
    'BWP': 'P',
    'NAD': '\$',
    'SZL': 'L',
    'LSL': 'L',
    'MZN': 'MT',
    'AOA': 'Kz',
    'CVE': '\$',
    'GMD': 'D',
    'GNF': 'FG',
    'LRD': '\$',
    'SLL': 'Le',
    'STN': 'Db',
    'CDF': 'FC',
    'RWF': 'R₣',
    'BIF': 'FBu',
    'DJF': 'Fdj',
    'ERN': 'Nfk',
    'SOS': 'S',
    'SCR': '₨',
    'MUR': '₨',
    'MGA': 'Ar',
    'KMF': 'CF',
    'MYR': 'RM',
    'IDR': 'Rp',
    'PHP': '₱',
    'VND': '₫',
    'LAK': '₭',
    'KHR': '៛',
    'MMK': 'K',
    'BDT': '৳',
    'LKR': '₨',
    'MVR': '.ރ',
    'NPR': '₨',
    'BTN': 'Nu.',
    'PKR': '₨',
    'AFN': '؋',
    'IRR': '﷼',
    'IQD': 'ع.د',
    'SYP': '£',
    'YER': '﷼',
    'UZS': 'лв',
    'KZT': '₸',
    'KGS': 'лв',
    'TJS': 'SM',
    'TMT': 'T',
    'AZN': '₼',
    'GEL': '₾',
    'AMD': '֏',
    'BGN': 'лв',
    'RON': 'lei',
    'HRK': 'kn',
    'RSD': 'дин.',
    'BAM': 'KM',
    'MKD': 'ден',
    'ALL': 'L',
    'MDL': 'L',
    'UAH': '₴',
    'BYN': 'Br',
    'LTL': 'Lt',
    'LVL': 'Ls',
    'EEK': 'kr',
    'ISK': 'kr',
    'FJD': '\$',
    'PGK': 'K',
    'SBD': '\$',
    'VUV': 'VT',
    'WST': 'T',
    'TOP': 'T\$',
    'TWD': 'NT\$',
    'MNT': '₮',
    'KPW': '₩',
    'CLP': '\$',
    'ARS': '\$',
    'UYU': '\$U',
    'PYG': 'Gs',
    'BOB': 'Bs',
    'PEN': 'S/',
    'COP': '\$',
    'VES': 'Bs',
    'GYD': '\$',
    'SRD': '\$',
    'FKP': '£',
    'SHP': '£',
    'GIP': '£',
    'JEP': '£',
    'GGP': '£',
    'IMP': '£',
    'XCD': '\$',
    'BBD': '\$',
    'BZD': 'BZ\$',
    'BMD': '\$',
    'KYD': '\$',
    'JMD': 'J\$',
    'TTD': 'TT\$',
    'HTG': 'G',
    'DOP': 'RD\$',
    'CUP': '₱',
    'NIO': 'C\$',
    'CRC': '₡',
    'GTQ': 'Q',
    'HNL': 'L',
    'PAB': 'B/.',
    'SVC': '₡',
  };

  // List of supported currencies with their details
  static const List<Map<String, String>> supportedCurrencies = [
    {'code': 'USD', 'symbol': '\$', 'name': 'US Dollar'},
    {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
    {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
    {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
    {'code': 'CNY', 'symbol': '¥', 'name': 'Chinese Yuan'},
    {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
    {'code': 'KRW', 'symbol': '₩', 'name': 'South Korean Won'},
    {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
    {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    {'code': 'CHF', 'symbol': 'CHF', 'name': 'Swiss Franc'},
    {'code': 'SEK', 'symbol': 'kr', 'name': 'Swedish Krona'},
    {'code': 'NOK', 'symbol': 'kr', 'name': 'Norwegian Krone'},
    {'code': 'DKK', 'symbol': 'kr', 'name': 'Danish Krone'},
    {'code': 'RUB', 'symbol': '₽', 'name': 'Russian Ruble'},
    {'code': 'BRL', 'symbol': 'R\$', 'name': 'Brazilian Real'},
    {'code': 'MXN', 'symbol': '\$', 'name': 'Mexican Peso'},
    {'code': 'ZAR', 'symbol': 'R', 'name': 'South African Rand'},
    {'code': 'SGD', 'symbol': 'S\$', 'name': 'Singapore Dollar'},
    {'code': 'HKD', 'symbol': 'HK\$', 'name': 'Hong Kong Dollar'},
    {'code': 'NZD', 'symbol': 'NZ\$', 'name': 'New Zealand Dollar'},
    {'code': 'THB', 'symbol': '฿', 'name': 'Thai Baht'},
    {'code': 'TRY', 'symbol': '₺', 'name': 'Turkish Lira'},
    {'code': 'PLN', 'symbol': 'zł', 'name': 'Polish Zloty'},
    {'code': 'CZK', 'symbol': 'Kč', 'name': 'Czech Koruna'},
    {'code': 'HUF', 'symbol': 'Ft', 'name': 'Hungarian Forint'},
    {'code': 'ILS', 'symbol': '₪', 'name': 'Israeli Shekel'},
    {'code': 'AED', 'symbol': 'د.إ', 'name': 'UAE Dirham'},
    {'code': 'SAR', 'symbol': '﷼', 'name': 'Saudi Riyal'},
    {'code': 'MYR', 'symbol': 'RM', 'name': 'Malaysian Ringgit'},
    {'code': 'IDR', 'symbol': 'Rp', 'name': 'Indonesian Rupiah'},
    {'code': 'PHP', 'symbol': '₱', 'name': 'Philippine Peso'},
    {'code': 'VND', 'symbol': '₫', 'name': 'Vietnamese Dong'},
    {'code': 'TWD', 'symbol': 'NT\$', 'name': 'Taiwan Dollar'},
  ];

  /// Get the currency symbol for a given currency code
  /// Returns the symbol if found, otherwise returns the currency code itself
  static String getCurrencySymbol(String currencyCode) {
    return currencySymbols[currencyCode.toUpperCase()] ?? currencyCode;
  }

  /// Format a currency amount with the appropriate symbol and formatting
  /// Uses NumberFormat.currency() for proper locale-specific formatting
  static String formatCurrencyAmount(double amount, String currencyCode) {
    try {
      final symbol = getCurrencySymbol(currencyCode);
      return NumberFormat.currency(
        symbol: symbol,
        decimalDigits: _getDecimalDigits(currencyCode),
      ).format(amount);
    } catch (e) {
      // Fallback to simple formatting if NumberFormat fails
      final symbol = getCurrencySymbol(currencyCode);
      return '$symbol${amount.toStringAsFixed(_getDecimalDigits(currencyCode))}';
    }
  }

  /// Get the number of decimal digits for a currency
  /// Most currencies use 2 decimal places, but some (like JPY, KRW) use 0
  static int _getDecimalDigits(String currencyCode) {
    final noDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP', 'PYG', 'RWF', 'UGX', 'KMF', 'GNF', 'MGA', 'XAF', 'XOF'];
    return noDecimalCurrencies.contains(currencyCode.toUpperCase()) ? 0 : 2;
  }

  /// Map currency symbols to currency codes with optional context-aware detection
  /// Used for parsing text that contains currency symbols
  ///
  /// For ambiguous symbols like ¥ (used by both JPY and CNY), the [context] parameter
  /// can be provided to intelligently determine the correct currency based on
  /// contextual clues in the transaction text.
  ///
  /// Example:
  /// ```dart
  /// symbolToCurrencyCode('¥', context: 'Beijing restaurant') // Returns 'CNY'
  /// symbolToCurrencyCode('¥', context: 'Tokyo sushi')       // Returns 'JPY'
  /// symbolToCurrencyCode('¥')                               // Returns 'JPY' (default)
  /// ```
  static String symbolToCurrencyCode(String symbol, {String? context}) {
    // Handle ¥ symbol ambiguity with context-aware detection
    if (symbol == '¥' && context != null) {
      final contextLower = context.toLowerCase();

      // Chinese indicators
      const chineseIndicators = [
        'beijing', 'shanghai', 'china', 'chinese', 'rmb', 'yuan', 'cny',
        'guangzhou', 'shenzhen', 'chengdu', 'hangzhou', 'nanjing', 'wuhan',
        'xian', 'tianjin', 'suzhou', 'chongqing', 'shenyang', 'dalian'
      ];

      // Japanese indicators
      const japaneseIndicators = [
        'tokyo', 'japan', 'japanese', 'yen', 'jpy', 'kyoto', 'osaka',
        'yokohama', 'nagoya', 'sapporo', 'kobe', 'fukuoka', 'hiroshima',
        'sendai', 'kitakyushu', 'chiba', 'sakai', 'niigata', 'hamamatsu'
      ];

      // Check for Chinese indicators
      for (final indicator in chineseIndicators) {
        if (contextLower.contains(indicator)) {
          return 'CNY';
        }
      }

      // Check for Japanese indicators
      for (final indicator in japaneseIndicators) {
        if (contextLower.contains(indicator)) {
          return 'JPY';
        }
      }

      // Default to JPY for backward compatibility when no context clues found
      return 'JPY';
    }

    // For non-ambiguous symbols or when no context provided, use original logic
    for (final entry in currencySymbols.entries) {
      if (entry.value == symbol) {
        return entry.key;
      }
    }
    return 'USD'; // Default fallback
  }

  /// Check if a currency code is supported
  static bool isSupportedCurrency(String currencyCode) {
    return currencySymbols.containsKey(currencyCode.toUpperCase());
  }

  /// Get all supported currency codes
  static List<String> getSupportedCurrencyCodes() {
    return supportedCurrencies.map((currency) => currency['code']!).toList();
  }

  /// Get currency name by code
  static String getCurrencyName(String currencyCode) {
    final currency = supportedCurrencies.firstWhere(
      (currency) => currency['code'] == currencyCode.toUpperCase(),
      orElse: () => {'name': currencyCode},
    );
    return currency['name']!;
  }
}
</file>

<file path="utils/raw_number_finder.dart">
import '../models/amount_candidate.dart';
import '../utils/amount_utils.dart';
import '../utils/currency_utils.dart';

/// Utility class that implements independent raw number finding logic
/// This finder discovers ALL numeric values in text without any filtering
/// for embedded numbers, as required by the PRD's "Trust but Verify" approach
class RawNumberFinder {
  /// Find all numbers in the given text and return them as AmountCandidate objects
  /// This method is completely independent and does not filter out any numbers
  /// 
  /// Supports:
  /// - Basic numbers: 123, 45.67
  /// - Abbreviated numbers: 100k, 2.5M, 1.2B
  /// - Numbers with thousands separators: 1,500
  /// - Numbers with currency symbols: $100, €50
  /// - Multiple numbers in one text
  /// - Embedded numbers in vendor names (NOT filtered out)
  /// 
  /// Returns List<AmountCandidate> with accurate start/end positions,
  /// parsed amounts, and detected currencies
  static List<AmountCandidate> findAllNumbers(String text) {
    if (text.isEmpty) return [];

    final List<AmountCandidate> candidates = [];

    // Comprehensive regex pattern to find all potential numbers
    // This pattern captures:
    // 1. Optional currency symbols at the start
    // 2. Numbers with optional thousands separators and decimals
    // 3. Optional abbreviation suffixes (k, m, b)
    // 4. Optional currency codes/names after the number
    final numberPattern = RegExp(
      r'(?:(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)\s*)?'  // Optional currency symbol
      r'(\d+(?:,\d{3})*(?:\.\d+)?)'  // Number with optional thousands separators and decimals
      r'([kKmMbB])?'  // Optional abbreviation
      r'(?:\s*(usd|eur|gbp|jpy|cny|inr|krw|aud|cad|chf|vnd|thb|try|ils|brl|rub|mxn|'
      r'dollars?|euros?|pounds?|yen|yuan|rupees?|won|pesos?|dong|baht|lira|shekel|reais?|rubles?))?',  // Optional currency name
      caseSensitive: false,
    );

    final matches = numberPattern.allMatches(text);

    for (final match in matches) {
      final currencySymbol = match.group(1);
      final numberText = match.group(2);
      final abbreviation = match.group(3);
      final currencyName = match.group(4);

      if (numberText == null || numberText.isEmpty) continue;



      // Normalize the number text by removing thousands separators
      String normalizedNumberText = numberText.replaceAll(',', '');

      // Parse the number with abbreviation support
      String fullNumberText = normalizedNumberText;
      if (abbreviation != null) {
        fullNumberText += abbreviation;
      }

      final amount = AmountUtils.parseAbbreviatedNumber(fullNumberText);
      if (amount == null) continue;

      // Determine currency from symbol or name
      String? currency;
      if (currencySymbol != null) {
        currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
      } else if (currencyName != null) {
        currency = _currencyNameToCode(currencyName.toLowerCase());
      }

      // If no currency found from symbol/name, try to detect from context
      if (currency == null) {
        currency = _detectCurrencyFromContext(text, match.start, match.end);
      }

      // Create candidate with accurate positions
      final candidate = AmountCandidate.fromRawNumberFinder(
        amount: amount,
        currency: currency,
        start: match.start,
        end: match.end,
        sourceText: match.group(0)!,
      );

      candidates.add(candidate);
    }

    // Also find standalone numbers that might not have been caught by the main pattern
    // This catches simple numbers like "123" or "45.67" without currency context
    final simpleNumberPattern = RegExp(r'\b\d+(?:\.\d+)?(?:[kKmMbB])?\b');
    final simpleMatches = simpleNumberPattern.allMatches(text);

    for (final match in simpleMatches) {
      final numberText = match.group(0)!;
      final amount = AmountUtils.parseAbbreviatedNumber(numberText);
      
      if (amount == null) continue;

      // Check if this match is already covered by a more comprehensive match
      final alreadyCovered = candidates.any((candidate) =>
          candidate.start <= match.start && candidate.end >= match.end);
      
      if (alreadyCovered) continue;

      // Try to detect currency from surrounding context
      final currency = _detectCurrencyFromContext(text, match.start, match.end);

      final candidate = AmountCandidate.fromRawNumberFinder(
        amount: amount,
        currency: currency,
        start: match.start,
        end: match.end,
        sourceText: numberText,
      );

      candidates.add(candidate);
    }

    // Sort candidates by position for consistent ordering
    candidates.sort((a, b) => a.start.compareTo(b.start));



    return candidates;
  }

  /// Convert currency name to currency code
  static String? _currencyNameToCode(String currencyName) {
    final nameMap = {
      'dollar': 'USD',
      'dollars': 'USD',
      'euro': 'EUR',
      'euros': 'EUR',
      'pound': 'GBP',
      'pounds': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupee': 'INR',
      'rupees': 'INR',
      'won': 'KRW',
      'peso': 'MXN',
      'pesos': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'real': 'BRL',
      'reais': 'BRL',
      'ruble': 'RUB',
      'rubles': 'RUB',
      'usd': 'USD',
      'eur': 'EUR',
      'gbp': 'GBP',
      'jpy': 'JPY',
      'cny': 'CNY',
      'inr': 'INR',
      'krw': 'KRW',
      'aud': 'AUD',
      'cad': 'CAD',
      'chf': 'CHF',
      'vnd': 'VND',
      'thb': 'THB',
      'try': 'TRY',
      'ils': 'ILS',
      'brl': 'BRL',
      'rub': 'RUB',
      'mxn': 'MXN',
    };

    return nameMap[currencyName];
  }

  /// Detect currency from the surrounding context of a number
  static String? _detectCurrencyFromContext(String text, int start, int end) {
    // Look for currency indicators within a reasonable distance from the number
    const contextWindow = 20;
    final contextStart = (start - contextWindow).clamp(0, text.length);
    final contextEnd = (end + contextWindow).clamp(0, text.length);
    final contextText = text.substring(contextStart, contextEnd);

    // Check for currency symbols
    final currencySymbolPattern = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = currencySymbolPattern.firstMatch(contextText);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: contextText);
    }

    // Check for currency codes and names
    final currencyNamePattern = RegExp(
      r'\b(usd|eur|gbp|jpy|cny|inr|krw|aud|cad|chf|vnd|thb|try|ils|brl|rub|mxn|'
      r'dollars?|euros?|pounds?|yen|yuan|rupees?|won|pesos?|dong|baht|lira|shekel|reais?|rubles?)\b',
      caseSensitive: false,
    );
    final nameMatch = currencyNamePattern.firstMatch(contextText);
    if (nameMatch != null) {
      return _currencyNameToCode(nameMatch.group(1)!.toLowerCase());
    }

    return null;
  }
}
</file>

<file path="widgets/category_picker_dialog.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/transaction_model.dart';

class CategoryPickerDialog extends StatefulWidget {
  final TransactionType transactionType;
  final String? initialCategoryId;
  
  const CategoryPickerDialog({
    Key? key,
    required this.transactionType,
    this.initialCategoryId,
  }) : super(key: key);

  @override
  State<CategoryPickerDialog> createState() => _CategoryPickerDialogState();
}

class _CategoryPickerDialogState extends State<CategoryPickerDialog> {
  String? _selectedCategoryId;
  String _searchQuery = '';
  late TextEditingController _searchController;
  
  @override
  void initState() {
    super.initState();
    _selectedCategoryId = widget.initialCategoryId;
    _searchController = TextEditingController();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context);
    
    // Filter categories by transaction type and search query
    final availableCategories = provider.categories
        .where((category) => category.type == widget.transactionType)
        .where((category) => 
            _searchQuery.isEmpty || 
            category.name.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList();
    
    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Select ${_getTransactionTypeLabel()} Category'),
          const SizedBox(height: 8),
          Text(
            'This will help improve automatic categorization for similar transactions.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400, // Fixed height for scrollable content
        child: Column(
          children: [
            // Search field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Search categories',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),
            
            // Categories grid
            Expanded(
              child: availableCategories.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 48,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No categories found',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          if (_searchQuery.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              'Try a different search term',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    )
                  : GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 2.5,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: availableCategories.length,
                      itemBuilder: (context, index) {
                        final category = availableCategories[index];
                        final isSelected = _selectedCategoryId == category.id;
                        
                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedCategoryId = category.id;
                            });
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: isSelected 
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.outline,
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              color: isSelected 
                                  ? theme.colorScheme.primaryContainer.withOpacity(0.3)
                                  : null,
                            ),
                            padding: const EdgeInsets.all(12),
                            child: Row(
                              children: [
                                Text(
                                  category.icon,
                                  style: const TextStyle(fontSize: 20),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    category.name,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: isSelected 
                                          ? FontWeight.w600 
                                          : FontWeight.normal,
                                      color: isSelected 
                                          ? theme.colorScheme.primary
                                          : null,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _selectedCategoryId != null
              ? () => Navigator.of(context).pop(_selectedCategoryId)
              : null,
          child: const Text('Select'),
        ),
      ],
    );
  }
  
  String _getTransactionTypeLabel() {
    switch (widget.transactionType) {
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.income:
        return 'Income';
      case TransactionType.loan:
        return 'Loan';
    }
  }
}

/// Utility function to show the category picker dialog
Future<String?> showCategoryPickerDialog({
  required BuildContext context,
  required TransactionType transactionType,
  String? initialCategoryId,
}) {
  return showDialog<String>(
    context: context,
    builder: (context) => CategoryPickerDialog(
      transactionType: transactionType,
      initialCategoryId: initialCategoryId,
    ),
  );
}
</file>

<file path="widgets/quick_reply_widget.dart">
import 'package:flutter/material.dart';

/// A reusable widget that displays horizontal quick reply buttons for user interaction
class QuickReplyWidget extends StatelessWidget {
  final List<String> replyOptions;
  final Function(String) onReplySelected;
  final EdgeInsets? padding;
  final double? spacing;
  final bool enabled;

  const QuickReplyWidget({
    super.key,
    required this.replyOptions,
    required this.onReplySelected,
    this.padding,
    this.spacing,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    if (replyOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      child: Wrap(
        spacing: spacing ?? 8.0,
        runSpacing: 8.0,
        children: replyOptions.map((option) {
          return _buildReplyButton(
            context,
            option,
            colorScheme,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildReplyButton(
    BuildContext context,
    String option,
    ColorScheme colorScheme,
  ) {
    return Material(
      child: InkWell(
        onTap: enabled ? () => onReplySelected(option) : null,
        borderRadius: BorderRadius.circular(20.0),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 8.0,
          ),
          decoration: BoxDecoration(
            color: enabled 
                ? colorScheme.secondaryContainer
                : colorScheme.surfaceVariant.withOpacity(0.5),
            borderRadius: BorderRadius.circular(20.0),
            border: Border.all(
              color: enabled 
                  ? colorScheme.outline.withOpacity(0.3)
                  : colorScheme.outline.withOpacity(0.1),
              width: 1.0,
            ),
          ),
          child: Text(
            option,
            style: TextStyle(
              color: enabled 
                  ? colorScheme.onSecondaryContainer
                  : colorScheme.onSurfaceVariant.withOpacity(0.5),
              fontSize: 14.0,
              fontWeight: FontWeight.w500,
            ),
            semanticsLabel: 'Quick reply: $option',
          ),
        ),
      ),
    );
  }
}

/// A specialized quick reply widget for transaction type selection
class TransactionTypeQuickReply extends StatelessWidget {
  final Function(String) onTypeSelected;
  final bool enabled;

  const TransactionTypeQuickReply({
    super.key,
    required this.onTypeSelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget(
      replyOptions: const ['Expense', 'Income', 'Cancel'],
      onReplySelected: onTypeSelected,
      enabled: enabled,
    );
  }
}

/// A specialized quick reply widget for category selection
class CategoryQuickReply extends StatelessWidget {
  final List<String> categories;
  final Function(String) onCategorySelected;
  final bool enabled;

  const CategoryQuickReply({
    super.key,
    required this.categories,
    required this.onCategorySelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final options = [...categories, 'Other', 'Cancel'];
    
    return QuickReplyWidget(
      replyOptions: options,
      onReplySelected: onCategorySelected,
      enabled: enabled,
    );
  }
}
</file>

<file path="widgets/startup_loading_widget.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/startup_service.dart';

/// Widget that displays during app startup while services are initializing
class StartupLoadingWidget extends StatefulWidget {
  const StartupLoadingWidget({super.key});

  @override
  State<StartupLoadingWidget> createState() => _StartupLoadingWidgetState();
}

class _StartupLoadingWidgetState extends State<StartupLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // Setup animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
    
    // Start animations
    _pulseController.repeat(reverse: true);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Consumer<StartupService>(
        builder: (context, startupService, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // App logo/icon with pulse animation
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(60),
                            boxShadow: [
                              BoxShadow(
                                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.account_balance_wallet,
                            size: 60,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // App title
                  Text(
                    'Money Lover Chat',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    'Your AI-powered expense tracker',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  
                  const SizedBox(height: 60),
                  
                  // Progress indicator
                  SizedBox(
                    width: 200,
                    child: LinearProgressIndicator(
                      backgroundColor: theme.colorScheme.surfaceVariant,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Service initialization status
                  _buildServiceStatus(context, startupService),
                  
                  const SizedBox(height: 40),
                  
                  // Loading tips
                  _buildLoadingTips(context),
                  
                  // Error handling
                  if (startupService.hasAnyServiceFailed)
                    _buildErrorSection(context, startupService),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildServiceStatus(BuildContext context, StartupService startupService) {
    final theme = Theme.of(context);
    final statuses = startupService.serviceStatuses;
    
    return Column(
      children: [
        Text(
          'Initializing services...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 16),
        ...statuses.entries.map((entry) {
          final serviceName = entry.key;
          final status = entry.value;
          
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _getStatusIcon(status.state, theme),
                const SizedBox(width: 8),
                Text(
                  _getServiceDisplayName(serviceName),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _getStatusIcon(ServiceInitializationState state, ThemeData theme) {
    switch (state) {
      case ServiceInitializationState.pending:
        return Icon(
          Icons.schedule,
          size: 16,
          color: theme.colorScheme.onSurfaceVariant,
        );
      case ServiceInitializationState.loading:
        return SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        );
      case ServiceInitializationState.ready:
        return Icon(
          Icons.check_circle,
          size: 16,
          color: Colors.green,
        );
      case ServiceInitializationState.failed:
        return Icon(
          Icons.error,
          size: 16,
          color: Colors.red,
        );
    }
  }

  String _getServiceDisplayName(String serviceName) {
    switch (serviceName) {
      case 'theme':
        return 'Theme Service';
      case 'transaction':
        return 'Transaction Service';
      case 'mlkit':
        return 'ML Kit Service';
      default:
        return serviceName;
    }
  }

  Widget _buildLoadingTips(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 32),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            'Tip: You can type natural language like "spent \$25 on coffee" and the app will automatically categorize your expenses!',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSection(BuildContext context, StartupService startupService) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.warning,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            'Some services failed to initialize',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'The app will continue with limited functionality. You can retry initialization.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.red.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () => startupService.retryFailedServices(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
</file>

<file path="widgets/transaction_edit_dialog.dart">
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../models/transaction_model.dart';

class TransactionEditDialog extends StatefulWidget {
  final Transaction transaction;
  
  const TransactionEditDialog({
    Key? key,
    required this.transaction,
  }) : super(key: key);

  @override
  State<TransactionEditDialog> createState() => _TransactionEditDialogState();
}

class _TransactionEditDialogState extends State<TransactionEditDialog> {
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  late DateTime _selectedDate;
  late String _selectedCategoryId;
  late TransactionType _selectedType;
  
  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController(text: widget.transaction.amount.toString());
    _descriptionController = TextEditingController(text: widget.transaction.description);
    _selectedDate = widget.transaction.date;
    _selectedCategoryId = widget.transaction.categoryId;
    _selectedType = widget.transaction.type;
  }
  
  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context);
    
    return AlertDialog(
      title: const Text('Edit Transaction'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Amount field
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                prefixText: '\$',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
            ),
            const SizedBox(height: 16),
            
            // Transaction type selection
            DropdownButtonFormField<TransactionType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Transaction Type',
              ),
              items: TransactionType.values.map((type) {
                String label = type.name[0].toUpperCase() + type.name.substring(1);
                IconData icon;
                Color color;
                
                switch (type) {
                  case TransactionType.expense:
                    icon = Icons.arrow_upward;
                    color = Colors.red;
                    break;
                  case TransactionType.income:
                    icon = Icons.arrow_downward;
                    color = Colors.green;
                    break;
                  case TransactionType.loan:
                    icon = Icons.sync_alt;
                    color = Colors.orange;
                    break;
                }
                
                return DropdownMenuItem<TransactionType>(
                  value: type,
                  child: Row(
                    children: [
                      Icon(icon, color: color, size: 18),
                      const SizedBox(width: 8),
                      Text(label),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                    
                    // Update category when type changes
                    final categories = provider.categories.where((c) => c.type == value).toList();
                    if (categories.isNotEmpty) {
                      _selectedCategoryId = categories.first.id;
                    }
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            
            // Category selection
            DropdownButtonFormField<String>(
              value: _selectedCategoryId,
              decoration: const InputDecoration(
                labelText: 'Category',
              ),
              items: provider.categories
                  .where((category) => category.type == _selectedType)
                  .map((category) {
                return DropdownMenuItem<String>(
                  value: category.id,
                  child: Row(
                    children: [
                      Text(category.icon),
                      const SizedBox(width: 8),
                      Text(category.name),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategoryId = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            
            // Description field
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            
            // Date picker
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text('Date'),
              subtitle: Text(_formatDate(_selectedDate)),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                
                if (picked != null) {
                  setState(() {
                    _selectedDate = DateTime(
                      picked.year,
                      picked.month,
                      picked.day,
                      _selectedDate.hour,
                      _selectedDate.minute,
                    );
                  });
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () {
            try {
              final amount = double.parse(_amountController.text);
              
              if (amount <= 0) {
                _showErrorSnackBar('Amount must be greater than zero');
                return;
              }
              
              final updatedTransaction = widget.transaction.copyWith(
                amount: amount,
                type: _selectedType,
                categoryId: _selectedCategoryId,
                date: _selectedDate,
                description: _descriptionController.text.trim(),
              );
              
              Navigator.of(context).pop(updatedTransaction);
            } catch (e) {
              _showErrorSnackBar('Please enter a valid amount');
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
  
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year;
    return '$day/$month/$year';
  }
}
</file>

<file path="widgets/transaction_message.dart">
import 'package:flutter/material.dart';

import '../models/transaction_model.dart';
import '../utils/currency_utils.dart';

class TransactionMessage extends StatelessWidget {
  final Transaction transaction;
  final Category category;
  final Function(Transaction) onEdit;
  final Function(String) onDelete;

  const TransactionMessage({
    Key? key,
    required this.transaction,
    required this.category,
    required this.onEdit,
    required this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.brightness == Brightness.light 
            ? Colors.white 
            : Colors.grey.shade900,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Transaction header with category and amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    category.icon,
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    category.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.brightness == Brightness.light 
                          ? Colors.black87 
                          : Colors.white,
                    ),
                  ),
                ],
              ),
              Text(
                CurrencyUtils.formatCurrencyAmount(transaction.amount, transaction.currencyCode),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getTransactionColor(transaction.type, theme),
                ),
              ),
            ],
          ),
          
          // Transaction description if available
          if (transaction.description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                transaction.description,
                style: TextStyle(
                  fontSize: 14,
                  color: theme.brightness == Brightness.light 
                      ? Colors.black54 
                      : Colors.grey.shade300,
                ),
              ),
            ),
          
          // Transaction date
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              _formatDate(transaction.date),
              style: TextStyle(
                fontSize: 12,
                color: theme.brightness == Brightness.light 
                    ? Colors.black45 
                    : Colors.grey.shade400,
              ),
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Edit button
                TextButton.icon(
                  onPressed: () => onEdit(transaction),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    minimumSize: const Size(0, 30),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
                const SizedBox(width: 8),
                // Delete button
                TextButton.icon(
                  onPressed: () => onDelete(transaction.id),
                  icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                  label: const Text('Delete', style: TextStyle(color: Colors.red)),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    minimumSize: const Size(0, 30),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getTransactionColor(TransactionType type, ThemeData theme) {
    switch (type) {
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.income:
        return Colors.green;
      case TransactionType.loan:
        return Colors.orange;
    }
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    final hours = dateTime.hour.toString().padLeft(2, '0');
    final minutes = dateTime.minute.toString().padLeft(2, '0');
    final time = '$hours:$minutes';
    
    if (date == today) {
      return 'Today, $time';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return 'Yesterday, $time';
    } else {
      final month = dateTime.month.toString().padLeft(2, '0');
      final day = dateTime.day.toString().padLeft(2, '0');
      return '$day/$month, $time';
    }
  }
}
</file>

<file path="audio_recorder.dart">
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:assets_audio_player/assets_audio_player.dart';
import 'package:record/record.dart' as rec;

class AudioRecorder {
  final _audioRecorder = rec.AudioRecorder();
  // final _audioPlayer = AssetsAudioPlayer();
  String? _recordedFilePath;
  bool _isRecording = false;
  bool _isPlaying = false;

  /// Initialize the recorder
  Future<void> init() async {
    final hasPermission = await _audioRecorder.hasPermission();
    if (!hasPermission) {
      throw Exception('Microphone permission not granted');
    }
  }

  /// Start recording audio
  Future<void> startRecording() async {
    try {
      if (await _audioRecorder.hasPermission()) {
        final rec.AudioEncoder encoder;
        if (kIsWeb) {
          _recordedFilePath = '';
          encoder = rec.AudioEncoder.opus;
        } else {
          final dir = await getApplicationDocumentsDirectory();
          _recordedFilePath =
              '${dir.path}/audio_${DateTime.now().millisecondsSinceEpoch}.m4a';
          encoder = rec.AudioEncoder.aacLc;
        }
        await _audioRecorder.start(
          rec.RecordConfig(encoder: encoder),
          path: _recordedFilePath!,
        );
        _isRecording = true;
      }
    } catch (e) {
      print('Error starting recording: $e');
      _isRecording = false;
    }
  }

  /// Stop recording audio
  Future<String?> stopRecording() async {
    try {
      if (_isRecording) {
        _recordedFilePath = await _audioRecorder.stop();
        _isRecording = false;
        return _recordedFilePath;
      }
      return null;
    } catch (e) {
      print('Error stopping recording: $e');
      _isRecording = false;
      return null;
    }
  }

  /// Play recorded audio
  Future<void> playRecording() async {
    // TODO: Implement audio playback when assets_audio_player is available
    print('Audio playback temporarily disabled');
    /*
    if (_recordedFilePath != null && _recordedFilePath!.isNotEmpty) {
      try {
        if (kIsWeb) {
          await _audioPlayer.open(
            Audio.network(_recordedFilePath!),
            autoStart: true,
          );
        } else {
          await _audioPlayer.open(
            Audio.file(_recordedFilePath!),
            autoStart: true,
          );
        }
        _isPlaying = true;

        // Update playing status when audio completes
        _audioPlayer.playlistAudioFinished.listen((_) {
          _isPlaying = false;
        });
      } catch (e) {
        print('Error playing recording: $e');
        _isPlaying = false;
      }
    }
    */
  }

  /// Stop playing audio
  Future<void> stopPlaying() async {
    // TODO: Implement stop playback when assets_audio_player is available
    _isPlaying = false;
    /*
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
    } catch (e) {
      print('Error stopping playback: $e');
    }
    */
  }

  /// Delete recorded audio file
  Future<void> deleteRecording() async {
    if (_recordedFilePath != null) {
      if (!kIsWeb) {
        try {
          final file = File(_recordedFilePath!);
          if (await file.exists()) {
            await file.delete();
          }
          _recordedFilePath = null;
        } catch (e) {
          print('Error deleting recording: $e');
        }
      } else {
        _recordedFilePath = null;
      }
    }
  }

  /// Check if currently recording
  bool get isRecording => _isRecording;

  /// Check if currently playing
  bool get isPlaying => _isPlaying;

  /// Get the path of recorded file
  String? get recordedFilePath => _recordedFilePath;

  /// Dispose resources
  Future<void> dispose() async {
    // await _audioPlayer.dispose();
    await _audioRecorder.dispose();
  }
}
</file>

<file path="file_upload.dart">
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:file_picker/file_picker.dart';

/// Helper class for handling file uploads
class FileUploadHelper {
  static final _uuid = Uuid();

  /// Upload a file and return its path
  /// Returns null if the operation was cancelled or failed
  static Future<String?> uploadFile() async {
    try {
      // Create file picker instance
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null) {
        // Get the temporary directory
        final tempDir = await getTemporaryDirectory();

        // Generate unique filename
        final filename = '${_uuid.v4()}_${result.files.first.name}';
        final filePath = '${tempDir.path}/$filename';

        // Copy file to app's temporary directory
        final file = File(result.files.first.path!);
        await file.copy(filePath);

        return filePath;
      }
      return null;
    } catch (e) {
      print('Error uploading file: $e');
      return null;
    }
  }

  /// Example usage in a widget
  static Future<void> handleFileUpload(BuildContext context) async {
    final filePath = await uploadFile();
    if (filePath != null) {
      // Do something with the file
      print('File uploaded: $filePath');
    }
  }
}
</file>

<file path="image_upload.dart">
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';

/// Helper class for handling image uploads
class ImageUploadHelper {
  static final ImagePicker _picker = ImagePicker();

  /// Pick an image from the gallery
  /// Returns the File object of the selected image, or null if cancelled
  static Future<Uint8List?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // Compress image to reduce size
      );

      if (image != null) {
        return await image.readAsBytes();
      }
      return null;
    } catch (e) {
      print('Error picking image from gallery: \$e');
      return null;
    }
  }

  /// Capture an image using the camera
  /// Returns the File object of the captured image, or null if cancelled
  static Future<Uint8List?> captureImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80, // Compress image to reduce size
      );

      if (image != null) {
        return await image.readAsBytes();
      }
      return null;
    } catch (e) {
      print('Error capturing image: \$e');
      return null;
    }
  }

  /// Example usage of how to handle the returned File
  static Future<void> handleImageSelection({required bool fromCamera}) async {
    Uint8List? imageFile =
        fromCamera ? await captureImage() : await pickImageFromGallery();

    if (imageFile != null) {
      // Do something with the image file
      // For example, upload to server or display in UI
      print('Image selected');
    }
  }
}
</file>

<file path="main.dart">
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;

import 'models/transaction_model.dart';
import 'services/storage_service.dart';
import 'services/parser/mlkit_parser_service.dart';
import 'services/startup_service.dart';
import 'navigation/app_navigation.dart';
import 'theme.dart';

void main() async {
  final startupStartTime = DateTime.now();
  developer.Timeline.startSync('App Startup');

  WidgetsFlutterBinding.ensureInitialized();

  // Only initialize essential storage service synchronously
  developer.Timeline.startSync('Storage Service Init');
  final storageService = StorageService();
  await storageService.init();
  developer.Timeline.finishSync();

  final storageInitTime = DateTime.now();
  developer.log('Storage service initialized in ${storageInitTime.difference(startupStartTime).inMilliseconds}ms',
      name: 'StartupPerformance');

  // Create startup service for coordinated initialization
  final startupService = StartupService.getInstance(storageService);

  developer.Timeline.finishSync();

  runApp(MultiProvider(
    providers: [
      // Provide startup service for coordination
      ChangeNotifierProvider<StartupService>.value(value: startupService),
      // Provide nullable services initially - they will be populated by StartupService
      Provider<ThemeProvider?>.value(value: null),
      Provider<TransactionProvider?>.value(value: null),
      Provider<MlKitParserService?>.value(value: null),
    ],
    child: const MoneyLoverChatApp(),
  ));

  final uiStartTime = DateTime.now();
  developer.log('UI started in ${uiStartTime.difference(startupStartTime).inMilliseconds}ms',
      name: 'StartupPerformance');
}

class MoneyLoverChatApp extends StatelessWidget {
  const MoneyLoverChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Use Consumer to handle nullable ThemeProvider during startup
    return Consumer<StartupService>(
      builder: (context, startupService, child) {
        final themeProvider = startupService.themeProvider;

        return MaterialApp(
          title: 'Money Lover Chat',
          // Use default light theme if ThemeProvider not ready yet
          theme: themeProvider?.themeData ?? ThemeData.light(),
          debugShowCheckedModeBanner: false,
          home: const AppNavigation(),
        );
      },
    );
  }
}
</file>

<file path="theme.dart">
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppTheme {
  // Material 3 seed colors
  static const Color primarySeedLight = Color(0xFF6750A4); // Purple primary
  static const Color primarySeedDark = Color(0xFFD0BCFF); // Purple primary variant
  
  // Transaction type colors
  static const Color incomeColor = Color(0xFF66BB6A); // Green
  static const Color expenseColor = Color(0xFFF44336); // Red
  static const Color loanColor = Color(0xFFFF9800); // Orange
  
  // Material 3 surfaces
  static const Color surfaceLight = Color(0xFFFFFBFE);
  static const Color surfaceDark = Color(0xFF1C1B1F);
  
  // Material 3 on-surfaces
  static const Color onSurfaceLight = Color(0xFF1C1B1F);
  static const Color onSurfaceDark = Color(0xFFE6E1E5);
  
  // Material 3 surface variants
  static const Color surfaceVariantLight = Color(0xFFE7E0EC);
  static const Color surfaceVariantDark = Color(0xFF49454F);
  
  // Chat bubble colors
  static const Color userBubbleLight = Color(0xFFE8DEF8); // Purple light variant
  static const Color userBubbleDark = Color(0xFF4F378B); // Purple dark variant
  static const Color botBubbleLight = Color(0xFFF5F5F5);
  static const Color botBubbleDark = Color(0xFF303030);

  // Light theme using Material 3
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primarySeedLight,
      brightness: Brightness.light,
      surfaceTint: primarySeedLight,
    ),
    scaffoldBackgroundColor: surfaceLight,
    textTheme: GoogleFonts.robotoTextTheme(ThemeData.light().textTheme).apply(
      bodyColor: onSurfaceLight,
      displayColor: onSurfaceLight,
    ),
    appBarTheme: AppBarTheme(
      centerTitle: false,
      backgroundColor: surfaceLight,
      foregroundColor: onSurfaceLight,
      elevation: 0,
      scrolledUnderElevation: 2,
    ),
    cardTheme: CardThemeData(
      color: surfaceLight,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(0),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: ButtonStyle(
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: surfaceLight,
      selectedItemColor: primarySeedLight,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
      elevation: 3,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: surfaceVariantLight.withOpacity(0.4),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: primarySeedLight, width: 1),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    ),
    chipTheme: ChipThemeData(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
    ),
    dividerTheme: const DividerThemeData(
      space: 24,
      thickness: 1,
      indent: 16,
      endIndent: 16,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    navigationBarTheme: NavigationBarThemeData(
      labelTextStyle: WidgetStateProperty.all(
        const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      elevation: 3,
    ),
  );

  // Dark theme using Material 3
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primarySeedDark,
      brightness: Brightness.dark,
      surfaceTint: primarySeedDark,
    ),
    scaffoldBackgroundColor: surfaceDark,
    textTheme: GoogleFonts.robotoTextTheme(ThemeData.dark().textTheme).apply(
      bodyColor: onSurfaceDark,
      displayColor: onSurfaceDark,
    ),
    appBarTheme: AppBarTheme(
      centerTitle: false,
      backgroundColor: surfaceDark,
      foregroundColor: onSurfaceDark,
      elevation: 0,
      scrolledUnderElevation: 2,
    ),
    cardTheme: CardThemeData(
      color: surfaceDark,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(0),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: ButtonStyle(
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: surfaceDark,
      selectedItemColor: primarySeedDark,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
      elevation: 3,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: surfaceVariantDark.withOpacity(0.4),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: primarySeedDark, width: 1),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    ),
    chipTheme: ChipThemeData(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
    ),
    dividerTheme: const DividerThemeData(
      space: 24,
      thickness: 1,
      indent: 16,
      endIndent: 16,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    navigationBarTheme: NavigationBarThemeData(
      labelTextStyle: WidgetStateProperty.all(
        const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      elevation: 3,
    ),
  );
}

class ThemeProvider with ChangeNotifier {
  bool _isDarkMode = false; // Default to light mode for immediate rendering
  final String _themeKey = 'isDarkMode';
  bool _isLoading = true;
  bool _isInitialized = false;

  ThemeProvider() {
    // Don't load preferences synchronously - set default theme immediately
    // Preferences will be loaded asynchronously via initialize() method
  }

  bool get isDarkMode => _isDarkMode;
  ThemeData get themeData => _isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;

  /// Initialize the theme provider asynchronously
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadThemePreference();
      _isInitialized = true;
      _isLoading = false;
    } catch (e) {
      print('Failed to load theme preference: $e');
      _isLoading = false;
      // Keep default theme on error
    }
    notifyListeners();
  }

  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTheme = prefs.getBool(_themeKey) ?? false;

    // Only update and notify if the theme actually changed
    if (_isDarkMode != savedTheme) {
      _isDarkMode = savedTheme;
    }
  }

  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_themeKey, _isDarkMode);
    notifyListeners();
  }
}
</file>

<file path="video_recorder.dart">
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class VideoRecorder {
  VideoPlayerController? _videoController;
  String? _videoPath;

  /// Initialize video player for a specific file
  Future<void> initializePlayer(String filePath) async {
    _videoController?.dispose();
    _videoPath = filePath;
    _videoController = VideoPlayerController.file(File(filePath));
    await _videoController?.initialize();
  }

  /// Get preview widget for video
  Widget? getVideoPreview() {
    if (_videoController == null || !_videoController!.value.isInitialized) {
      return null;
    }
    return AspectRatio(
      aspectRatio: _videoController!.value.aspectRatio,
      child: VideoPlayer(_videoController!),
    );
  }

  /// Play the video
  Future<void> play() async {
    await _videoController?.play();
  }

  /// Pause the video
  Future<void> pause() async {
    await _videoController?.pause();
  }

  /// Get the current video path
  String? get videoPath => _videoPath;

  /// Dispose resources
  Future<void> dispose() async {
    await _videoController?.dispose();
    _videoController = null;
  }
}
</file>

</files>
