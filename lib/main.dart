import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'models/transaction_model.dart';
import 'services/storage_service.dart';
import 'services/parser/mlkit_parser_service.dart';
import 'navigation/app_navigation.dart';
import 'theme.dart';
import 'utils/startup_timer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Start performance measurement
  final timer = StartupTimer.instance;
  timer.mark('app-start');

  // Initialize storage service (fast, essential)
  final storageService = StorageService();
  await storageService.init();
  timer.mark('storage-ready');

  // Create a provider for ML Kit service that will be populated later
  final mlKitProvider = ValueNotifier<MlKitParserService?>(null);

  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ChangeNotifierProvider(create: (_) => TransactionProvider(storageService)),
      // Provide nullable ML Kit service initially
      ChangeNotifierProvider<ValueNotifier<MlKitParserService?>>.value(value: mlKitProvider),
    ],
    child: const MoneyLoverChatApp(),
  ));

  timer.mark('ui-shown');

  // Initialize ML Kit in background after first frame
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _initializeMlKitInBackground(storageService, mlKitProvider, timer);
  });
}

/// Initialize ML Kit service in background after UI is shown
Future<void> _initializeMlKitInBackground(
  StorageService storageService,
  ValueNotifier<MlKitParserService?> mlKitProvider,
  StartupTimer timer,
) async {
  try {
    developer.log('Starting background ML Kit initialization', name: 'StartupPerformance');

    // Initialize ML Kit service
    final mlKitService = await MlKitParserService.getInstance(storageService);

    // Update the provider with the initialized service
    mlKitProvider.value = mlKitService;

    timer.mark('mlkit-ready');
    timer.measure('ui-shown', 'mlkit-ready');

    developer.log('ML Kit initialized successfully in background', name: 'StartupPerformance');

    // Print performance summary
    timer.printSummary();

  } catch (e) {
    developer.log('Background ML Kit initialization failed: $e', name: 'StartupPerformance');
    // ML Kit failure is not critical - app will continue with fallback parsing
    timer.mark('mlkit-failed');
  }
}

class MoneyLoverChatApp extends StatelessWidget {
  const MoneyLoverChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return MaterialApp(
      title: 'Money Lover Chat',
      theme: themeProvider.themeData,
      debugShowCheckedModeBanner: false,
      home: const AppNavigation(),
    );
  }
}