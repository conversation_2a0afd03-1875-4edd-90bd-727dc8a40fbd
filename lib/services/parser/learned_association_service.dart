import 'dart:convert';
import '../../models/transaction_model.dart';
import '../storage_service.dart';
import 'learned_category_storage.dart';

/// Data class representing a learned association between text and transaction attributes
class LearnedAssociation {
  final TransactionType? type;
  final String? categoryId;
  final double? confirmedAmount;
  final DateTime lastUpdated;
  final int confidence;

  LearnedAssociation({
    this.type,
    this.categoryId,
    this.confirmedAmount,
    required this.lastUpdated,
    this.confidence = 1,
  });

  factory LearnedAssociation.fromJson(Map<String, dynamic> json) {
    return LearnedAssociation(
      type: json['type'] != null ? TransactionType.values.byName(json['type']) : null,
      categoryId: json['categoryId'],
      confirmedAmount: json['confirmedAmount']?.toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      confidence: json['confidence'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type?.name,
      'categoryId': categoryId,
      'confirmedAmount': confirmedAmount,
      'lastUpdated': lastUpdated.toIso8601String(),
      'confidence': confidence,
    };
  }

  LearnedAssociation copyWith({
    TransactionType? type,
    String? categoryId,
    double? confirmedAmount,
    DateTime? lastUpdated,
    int? confidence,
  }) {
    return LearnedAssociation(
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      confirmedAmount: confirmedAmount ?? this.confirmedAmount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      confidence: confidence ?? this.confidence,
    );
  }
}

/// Unified service for managing learned associations between text patterns and transaction attributes
class LearnedAssociationService {
  static const String _storageKey = 'learned_associations';
  static LearnedAssociationService? _instance;
  
  final StorageService _storageService;
  final LearnedCategoryStorage _legacyStorage;
  bool _isInitialized = false;
  bool _migrationCompleted = false;

  LearnedAssociationService._(this._storageService)
      : _legacyStorage = LearnedCategoryStorage(_storageService);

  /// Get singleton instance
  static Future<LearnedAssociationService> getInstance(StorageService storageService) async {
    _instance ??= LearnedAssociationService._(storageService);
    if (!_instance!._isInitialized) {
      await _instance!._initialize();
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    _instance = null;
  }

  /// Initialize the service and perform data migration if needed
  Future<void> _initialize() async {
    if (_isInitialized) return;
    
    // Perform migration from legacy storage if needed
    if (!_migrationCompleted) {
      await _migrateFromLegacyStorage();
      _migrationCompleted = true;
    }
    
    _isInitialized = true;
  }

  /// Learn an association between text and transaction attributes
  Future<void> learn(String text, {TransactionType? type, String? categoryId, double? confirmedAmount}) async {
    if (!_isInitialized) await _initialize();

    if (text.trim().isEmpty || (type == null && categoryId == null && confirmedAmount == null)) {
      return; // Nothing to learn
    }

    try {
      final normalizedText = _normalizeText(text);
      final vendorName = _extractVendorName(normalizedText);
      final keyToStore = vendorName ?? normalizedText;
      
      final storedData = _storageService.getString(_storageKey);
      Map<String, dynamic> associationsMap = {};
      
      if (storedData != null) {
        try {
          associationsMap = jsonDecode(storedData);
        } catch (e) {
          // Storage is corrupted, start fresh
          print('Corrupted storage detected, starting fresh: $e');
          associationsMap = {};
        }
      }
      
      // Get existing association or create new one
      LearnedAssociation? existingAssociation;
      if (associationsMap.containsKey(keyToStore)) {
        existingAssociation = LearnedAssociation.fromJson(associationsMap[keyToStore]);
      }
      
      // Create updated association
      final updatedAssociation = LearnedAssociation(
        type: type ?? existingAssociation?.type,
        categoryId: categoryId ?? existingAssociation?.categoryId,
        confirmedAmount: confirmedAmount ?? existingAssociation?.confirmedAmount,
        lastUpdated: DateTime.now(),
        confidence: (existingAssociation?.confidence ?? 0) + 1,
      );
      
      associationsMap[keyToStore] = updatedAssociation.toJson();
      
      final updatedData = jsonEncode(associationsMap);
      await _storageService.setString(_storageKey, updatedData);
    } catch (e) {
      // Log error but don't throw to avoid breaking the learning flow
      print('Error learning association: $e');
    }
  }

  /// Retrieve a learned association for the given text
  Future<LearnedAssociation?> getAssociation(String text) async {
    if (!_isInitialized) await _initialize();
    
    try {
      final normalizedText = _normalizeText(text);
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return null;

      Map<String, dynamic> associationsMap;
      try {
        associationsMap = jsonDecode(storedData);
      } catch (e) {
        // Storage is corrupted, return null
        print('Corrupted storage detected in getAssociation: $e');
        return null;
      }
      
      // First try exact match
      if (associationsMap.containsKey(normalizedText)) {
        return LearnedAssociation.fromJson(associationsMap[normalizedText]);
      }
      
      // Then try to find a partial match with stored vendor names
      final extractedVendor = _extractVendorName(normalizedText);
      if (extractedVendor != null && associationsMap.containsKey(extractedVendor)) {
        return LearnedAssociation.fromJson(associationsMap[extractedVendor]);
      }
      
      // Finally, check if any stored pattern is contained in the text or vice versa
      for (final entry in associationsMap.entries) {
        final storedPattern = entry.key;
        final associationData = entry.value as Map<String, dynamic>;
        
        // Check both directions and also word-level matches
        if (_isPartialMatch(normalizedText, storedPattern)) {
          return LearnedAssociation.fromJson(associationData);
        }
      }
      
      return null;
    } catch (e) {
      // Log error but don't throw to avoid breaking the parsing flow
      print('Error getting association: $e');
      return null;
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, LearnedAssociation>> getAllAssociations() async {
    if (!_isInitialized) await _initialize();
    
    try {
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return {};

      Map<String, dynamic> associationsMap;
      try {
        associationsMap = jsonDecode(storedData);
      } catch (e) {
        // Storage is corrupted, return empty map
        print('Corrupted storage detected in getAllAssociations: $e');
        return {};
      }
      final result = <String, LearnedAssociation>{};
      
      for (final entry in associationsMap.entries) {
        result[entry.key] = LearnedAssociation.fromJson(entry.value);
      }
      
      return result;
    } catch (e) {
      print('Error getting all associations: $e');
      return {};
    }
  }

  /// Clear all learned data
  Future<void> clearAllData() async {
    await _storageService.remove(_storageKey);
  }

  /// Migrate data from legacy LearnedCategoryStorage
  Future<void> _migrateFromLegacyStorage() async {
    try {
      final legacyData = await _legacyStorage.getAllLearnedCategories();
      
      if (legacyData.isEmpty) return;
      
      // Check if we already have new format data
      final existingData = _storageService.getString(_storageKey);
      if (existingData != null) return; // Migration already done
      
      final associationsMap = <String, dynamic>{};
      
      // Convert legacy category-only data to new format
      for (final entry in legacyData.entries) {
        final association = LearnedAssociation(
          categoryId: entry.value,
          lastUpdated: DateTime.now(),
          confidence: 1,
        );
        associationsMap[entry.key] = association.toJson();
      }
      
      if (associationsMap.isNotEmpty) {
        final migratedData = jsonEncode(associationsMap);
        await _storageService.setString(_storageKey, migratedData);
        print('Migrated ${associationsMap.length} learned categories to new format');
      }
    } catch (e) {
      print('Error during migration: $e');
    }
  }

  /// Normalize text for consistent lookup (same as legacy implementation)
  String _normalizeText(String text) {
    return text
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^\w\s]'), '') // Remove special chars
        .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
  }

  /// Extract vendor name or key phrase from transaction description (enhanced for better matching)
  String? _extractVendorName(String text) {
    // Remove common transaction prefixes and suffixes
    String cleanText = text
        .replaceAll(RegExp(r'^(spent|paid|buy|bought|purchase|dinner|lunch|breakfast)\s+(at|in|from)?\s*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(for|on|at|in)\s+.*$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(restaurant|coffee|shop|store|market|cafe)$', caseSensitive: false), '')
        .trim();

    // Remove amounts and currency symbols to focus on vendor name
    cleanText = cleanText
        .replaceAll(RegExp(r'\$?\d+\.?\d*[kmb]?', caseSensitive: false), '') // Remove amounts like $25.50, 25.50, 25, 100k, 2m
        .replaceAll(RegExp(r'[£€¥₹₽]'), '') // Remove other currency symbols
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();

    // If the cleaned text is meaningful, use it
    if (cleanText.isNotEmpty && cleanText.length > 2) {
      return cleanText;
    }
    
    // Try to extract business name patterns (look for capitalized words or brands)
    final words = text.split(RegExp(r'\s+'));
    
    // Look for common business name patterns
    for (int i = 0; i < words.length; i++) {
      final word = words[i].toLowerCase();
      
      // Skip common transaction words and amounts
      if (['spent', 'paid', 'buy', 'bought', 'purchase', 'at', 'in', 'from', 'for', 'on', 'dinner', 'lunch', 'breakfast'].contains(word) ||
          RegExp(r'^\$?\d+\.?\d*[kmb]?$', caseSensitive: false).hasMatch(word)) {
        continue;
      }

      // Try to build a business name from this position
      final businessWords = <String>[];
      for (int j = i; j < words.length && businessWords.length < 3; j++) { // Reduced from 4 to 3 for more focused names
        final currentWord = words[j].toLowerCase();

        // Skip amounts
        if (RegExp(r'^\$?\d+\.?\d*[kmb]?$', caseSensitive: false).hasMatch(currentWord)) {
          continue;
        }

        // Stop at prepositions that usually end business names
        if (['for', 'on', 'with', 'and', 'at', 'in', 'from'].contains(currentWord) && businessWords.isNotEmpty) {
          break;
        }

        // For brand names, prefer the core name without generic suffixes
        if (['restaurant', 'coffee', 'shop', 'store', 'market', 'cafe'].contains(currentWord)) {
          // If we already have a business name, stop here to get the core brand
          if (businessWords.isNotEmpty) {
            break;
          }
          // Otherwise include it as part of the name
        }

        businessWords.add(currentWord);
      }
      
      if (businessWords.isNotEmpty) {
        final businessName = businessWords.join(' ');
        if (businessName.length > 2 && businessName.length < 30) {
          return businessName;
        }
      }
    }
    
    return null;
  }

  /// Check if two texts match partially using word-level comparison (same as legacy implementation)
  bool _isPartialMatch(String text1, String text2) {
    final words1 = text1.split(' ').where((w) => w.isNotEmpty).toSet();
    final words2 = text2.split(' ').where((w) => w.isNotEmpty).toSet();
    
    // If either text has common words with the other, consider it a match
    if (words1.intersection(words2).isNotEmpty) {
      return true;
    }
    
    // Also check substring matches for shorter patterns
    if (text1.length < text2.length && text2.contains(text1)) {
      return true;
    }
    if (text2.length < text1.length && text1.contains(text2)) {
      return true;
    }
    
    return false;
  }
}
