import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/mlkit_parser_service.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_entity_extractor.dart';

/// Integration tests for PRD v1.1.4 "Trust but Verify" implementation
/// 
/// These tests verify the complete end-to-end implementation of the multiple number
/// parsing approach described in PRD v1.1.4, ensuring that the ML Kit parser service
/// correctly implements the 4-step Trust but Verify approach:
/// 
/// 1. Call ML Kit service to extract entities
/// 2. Independently run raw number finder on original text
/// 3. Consolidate both lists into comprehensive candidates
/// 4. Apply ambiguity detection to consolidated list
/// 
/// The tests use real-world scenarios and verify that the service behaves correctly
/// in various edge cases and ambiguous situations.
void main() {
  group('PRD v1.1.4 Integration Tests', () {
    late MockStorageService mockStorage;
    late MlKitParserService service;

    setUp(() async {
      mockStorage = MockStorageService();
    });

    tearDown(() {
      MlKitParserService.resetInstance();
    });

    group('Trust but Verify Core Scenarios', () {
      test('should handle clear single amount with ML Kit detection', () async {
        // Scenario: ML Kit finds the amount, raw finder also finds it
        // Expected: Single amount selected, no ambiguity
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent 100 USD on coffee',
          entityText: '100 USD',
          start: 6,
          end: 13,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 100 USD on coffee');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(100.0));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should handle ML Kit miss but raw finder catch', () async {
        // Scenario: ML Kit misses the amount, but raw finder finds it
        // Expected: Raw finder amount selected, no ambiguity
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Coffee 50 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(50.0));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should trigger ambiguity for multiple non-embedded amounts', () async {
        // Scenario: Multiple clear amounts that could both be valid
        // Expected: Ambiguity triggered, user confirmation required
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'Transfer 100 USD, fee 5 USD',
          moneyEntities: [
            {'text': '100 USD', 'start': 9, 'end': 16},
            {'text': '5 USD', 'start': 22, 'end': 27},
          ],
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Transfer 100 USD, fee 5 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.needsAmountConfirmation, isTrue);
        expect(result.candidateAmounts, containsAll([100.0, 5.0]));
      });

      test('should prefer non-embedded over embedded amounts', () async {
        // Scenario: Embedded number in vendor name + clear amount
        // Expected: Clear amount selected, embedded ignored
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('dinner at lux69 100k vnd');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(100000.0));
        expect(result.transaction.currencyCode, equals('VND'));
        expect(result.needsAmountConfirmation, isFalse);
        // Should ignore embedded "69" in "lux69"
      });

      test('should handle all embedded numbers conservatively', () async {
        // Scenario: Only embedded numbers available
        // Expected: First embedded number selected, no ambiguity
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('hotel789 room456');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(789.0));
        expect(result.needsAmountConfirmation, isFalse);
        // Should select first embedded number by position
      });
    });

    group('PRD Edge Cases and Complex Scenarios', () {
      test('should handle abbreviated amounts correctly', () async {
        // Scenario: Abbreviated amounts like "2k", "1M"
        // Expected: Correct conversion and detection
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Salary 2k USD this month');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(2000.0));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should handle currency symbols correctly', () async {
        // Scenario: Currency symbols like $, €, ¥
        // Expected: Correct currency detection
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Lunch \$25 today');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(25.0));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should handle thousands separators correctly', () async {
        // Scenario: Numbers with thousands separators
        // Expected: Correct parsing of large numbers
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('House payment 1,500 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(1500.0));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should deduplicate identical amounts from different sources', () async {
        // Scenario: ML Kit and raw finder find the same amount
        // Expected: Single amount, no duplication
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Payment 200 USD completed',
          entityText: '200 USD',
          start: 8,
          end: 15,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Payment 200 USD completed');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.transaction.amount, equals(200.0));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.needsAmountConfirmation, isFalse);
      });
    });

    group('PRD Ambiguity Detection Scenarios', () {
      test('should trigger ambiguity for similar magnitude amounts', () async {
        // Scenario: Two amounts of similar magnitude
        // Expected: Ambiguity triggered
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Payment 100 or 150 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.needsAmountConfirmation, isTrue);
        expect(result.candidateAmounts, containsAll([100.0, 150.0]));
      });

      test('should not trigger ambiguity when one amount is clearly dominant', () async {
        // Scenario: One large amount, one small amount (different contexts)
        // Expected: Larger amount selected if contextually appropriate
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Purchase 1000 USD, tax 50 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        expect(result.needsAmountConfirmation, isTrue);
        expect(result.candidateAmounts, containsAll([1000.0, 50.0]));
        // Note: This should trigger ambiguity as both are valid transaction amounts
      });
    });

    group('PRD Fallback and Error Handling', () {
      test('should handle no amounts found gracefully', () async {
        // Scenario: No amounts detected by either ML Kit or raw finder
        // Expected: Graceful fallback or error handling
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Had lunch today');

        expect(result, isNotNull);
        // Should either find a fallback or indicate no amount found
        // The exact behavior depends on the fallback parser implementation
      });

      test('should handle complex number patterns gracefully', () async {
        // Scenario: Complex number patterns that could be ambiguous
        // Expected: Find all valid numbers and trigger ambiguity if needed
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Payment 100.50.25 USD or 200 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should find multiple valid numbers: 100.50, 25, and 200
        // This triggers ambiguity detection as expected
        expect(result.needsAmountConfirmation, isTrue);
        expect(result.candidateAmounts, containsAll([100.5, 25.0, 200.0]));
      });
    });
  });
}
