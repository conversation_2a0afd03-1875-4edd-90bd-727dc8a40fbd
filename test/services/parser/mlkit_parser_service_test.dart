import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/mlkit_parser_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_entity_extractor.dart';
import '../../test_data/sample_transactions.dart';

void main() {
  // Initialize Flutter bindings for tests that need platform services
  TestWidgetsFlutterBinding.ensureInitialized();
  group('MlKitParserService Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      // Always reset singleton before each test
      MlKitParserService.resetInstance();
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      // Reset singleton instance for clean tests
      print('Resetting MlKitParserService singleton instance');
      MlKitParserService.resetInstance();
    });

    group('Service Initialization', () {
      test('should create singleton instance with mock extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final instance1 = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
        final instance2 = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        expect(identical(instance1, instance2), isTrue);
      });

      test('should initialize with mock entity extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
        expect(service, isNotNull);
      });

      test('should handle entity extractor errors gracefully', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError('Mock initialization error');
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
        expect(service, isNotNull);

        // Service should still work by falling back to regex parsing
        final result = await service.parseTransaction('Spent \$25.50 on coffee');
        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50));
      });
    });

    group('Transaction Parsing with Mock Entity Extractor', () {
      test('should parse simple expense transactions with mock money entities', () async {
        // Test with mock entity extractor that returns money entities
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$25.50 on coffee',
          entityText: '\$25.50',
          start: 6,
          end: 12,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle empty entity results and fall back to regex', () async {
        // Test with mock that returns no entities
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50)); // Should be parsed by fallback regex
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should parse income transactions with mock money entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Received \$2000 salary',
          entityText: '\$2000',
          start: 9,
          end: 15,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Received \$2000 salary');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(2000.0));
        expect(result.transaction.type, equals(TransactionType.income));
      });

      test('should parse complex transactions with tags and multiple entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMultipleEntities(
          moneyText: '€150.75',
          moneyStart: 10,
          moneyEnd: 17,
          dateTimeText: 'yesterday',
          dateTimeStart: 25,
          dateTimeEnd: 34,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent €150.75 on dinner yesterday #restaurant');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(150.75));
        expect(result.transaction.tags, contains('restaurant'));
      });

      test('should handle non-money entities and fall back to regex', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithNonMoneyEntities();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50)); // Should be parsed by fallback
        expect(result.transaction.type, equals(TransactionType.expense));
      });
    });

    group('Fallback Behavior', () {
      test('should fall back to regex parsing when mock entity extractor throws error', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError('Mock parsing error');
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        // Should fall back to regex parsing and succeed
        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should fall back to regex when mock returns empty entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final testTexts = [
          'Spent \$25.50 on coffee',
          'Received €100 salary',
          'Lent £50 to friend',
        ];

        for (final text in testTexts) {
          final result = await service.parseTransaction(text);

          // Should get a result from fallback regex parsing
          expect(result, isNotNull);
          expect(result.transaction.amount, greaterThan(0.0));
        }
      });

      test('should handle malformed inputs gracefully', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final malformedTexts = [
          'random text without amounts',
          'abc def ghi',
          '',
        ];

        for (final text in malformedTexts) {
          final result = await service.parseTransaction(text);

          // Should not throw, but may return failed result
          expect(result, isNotNull);
        }
      });
    });

    group('Mock Entity Extractor Unit Tests', () {
      test('should parse USD amounts correctly with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createUSDScenario('\$25.50', 6, 12);
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should parse EUR amounts correctly with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createEURScenario('€150.75', 5, 12);
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Paid €150.75 for dinner');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(150.75));
      });

      test('should handle edge cases with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createEdgeCaseScenario(
          entityText: '\$1,234.56',
          start: 10,
          end: 19,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Transaction with \$1,234.56');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(1234.56));
      });

      test('should handle datetime entities with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithDateTimeEntity(
          text: 'Spent money yesterday',
          entityText: 'yesterday',
          start: 12,
          end: 21,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25 yesterday');

        expect(result, isNotNull);
        // Should fall back to regex for amount since no money entity in mock
        expect(result.transaction.amount, equals(25.0));
      });
    });

    group('Currency Handling', () {
      test('should detect USD currency from mock money entity', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$25.50 on coffee',
          entityText: '\$25.50',
          start: 6,
          end: 12,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should detect EUR currency from mock money entity', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Paid €150.75 for dinner',
          entityText: '€150.75',
          start: 5,
          end: 12,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Paid €150.75 for dinner');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('EUR'));
      });

      test('should use default currency when no currency detected', () async {
        await mockStorage.saveDefaultCurrency('EUR');
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent 25.50 on something',
          entityText: '25.50',
          start: 6,
          end: 11,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 25.50 on something');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('EUR'));
      });
    });

    group('Category Learning', () {
      test('should learn and apply category associations with mock extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        const text = 'Coffee \$5.50 at my local cafe';
        const categoryId = 'food';

        // Learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text again - should use learned association
        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should prioritize learned categories over automatic detection', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50';
        const learnedCategoryId = 'beverages';

        // Learn a specific category
        await service.learnCategory(text, learnedCategoryId);

        // Parse again - should use learned category
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals(learnedCategoryId));
        }
      });
    });

    group('Edge Cases', () {
      setUp(() {
        // Ensure fresh singleton for edge case tests
        MlKitParserService.resetInstance();
      });

      test('should handle empty input', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('');
        expect(result.hasError, isTrue);
      });

      test('should handle whitespace-only input', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('   \n\t  ');
        expect(result.hasError, isTrue);
      });

      test('should handle very long text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final longText = 'This is a very long transaction description that contains a lot of text and details about the purchase including the amount of \$25.50 spent on coffee at a local cafe with friends on a sunny afternoon during the weekend when everyone was relaxing and enjoying their time together';
        
        final result = await service.parseTransaction(longText);
        
        // Should handle long text gracefully
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
        }
      });

      test('should handle special characters and unicode', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Café & Restaurant! €25.50 🍽️ #food';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
          expect(result.transaction.currencyCode, equals('EUR'));
        }
      });

      test('should handle multiple amounts in text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Spent \$10 on coffee and \$15 on lunch';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          // Should pick one of the amounts (typically the first)
          expect([10.0, 15.0], contains(result.transaction.amount));
        }
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle parsing errors gracefully', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        // Test various problematic inputs
        final problematicInputs = [
          'invalid input with no clear structure',
          '###@@@%%% weird characters',
          'amount without numbers',
          'very confusing transaction description',
        ];

        for (final text in problematicInputs) {
          expect(() async {
            await service.parseTransaction(text);
          }, returnsNormally, reason: 'Should not throw for: $text');
        }
      });

      test('should provide meaningful error messages', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('no amount here');
        
        if (result.hasError) {
          expect(result.error, isNotNull);
          expect(result.error!.length, greaterThan(0));
        }
      });
    });

    group('Transaction ID and Metadata', () {
      test('should generate unique transaction IDs', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final result1 = await service.parseTransaction(text);
        final result2 = await service.parseTransaction(text);
        
        if (result1.isSuccess && result2.isSuccess) {
          expect(result1.transaction.id, isNot(equals(result2.transaction.id)));
        }
      });

      test('should set appropriate timestamps', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final beforeParse = DateTime.now();
        final result = await service.parseTransaction(text);
        final afterParse = DateTime.now();
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.date.isAfter(beforeParse.subtract(const Duration(seconds: 1))), isTrue);
          expect(result.transaction.date.isBefore(afterParse.add(const Duration(seconds: 1))), isTrue);
        }
      });

      test('should create meaningful descriptions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50 downtown location';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.description, isNotEmpty);
          expect(result.transaction.description.toLowerCase(), contains('coffee'));
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple parsing requests efficiently', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final testTexts = [
          'Coffee \$5.50',
          'Gas \$40.00',
          'Groceries \$85.30',
          'Dinner \$45.99',
          'Movie \$12.50',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 50; i++) {
          for (final text in testTexts) {
            await service.parseTransaction(text);
          }
        }

        stopwatch.stop();
        
        // Should complete in reasonable time (this is generous to account for MLKit overhead)
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });

      test('should handle singleton access efficiently', () async {
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 100; i++) {
          await MlKitParserService.getInstance(mockStorage);
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Reported Issues - Enhanced Fallback Logic', () {
      test('should fallback when MLKit returns empty entities', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // These should trigger fallback due to MLKit limitations
        final testCases = [
          'Spent ¥2500 on dinner',
          'Cost ¥3500 for shopping',
          'Paid ¥10000 for rent',
          'Beijing restaurant ¥45.50',
          'Tokyo sushi ¥1200',
        ];

        for (final text in testCases) {
          final result = await service.parseTransaction(text);

          // Should succeed either via MLKit or fallback
          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should parse successfully: $text');
          expect(result.transaction.amount, greaterThan(0),
                 reason: 'Should extract valid amount: $text');
        }
      });

      test('should fallback when MLKit fails to extract complete information', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          'grocery shopping ¥100',
          'food shopping ¥50',
          'clothes shopping ¥200',
          '-¥500 for toys',
          '- \$25.50 coffee',
        ];

        for (final text in testCases) {
          final result = await service.parseTransaction(text);

          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should handle via fallback: $text');
        }
      });
    });

    group('Reported Issues - Negative Number Handling', () {
      test('should detect negative amounts as expenses', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          '-¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee': TransactionType.expense,
          '-€100 for shopping': TransactionType.expense,
          '-£75.99 for dinner': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.type, equals(entry.value),
                   reason: 'Negative amount expense detection failed for: ${entry.key}');
            expect(result.transaction.amount, greaterThan(0),
                   reason: 'Amount should be positive for: ${entry.key}');
          }
        }
      });
    });

    group('Reported Issues - Context-Aware Currency Detection', () {
      test('should detect CNY for Chinese context', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'Beijing restaurant ¥45.50': 'CNY',
          'Shanghai taxi ¥25': 'CNY',
          'Guangzhou shopping ¥100': 'CNY',
          'China trip ¥500': 'CNY',
          'Chinese restaurant ¥80': 'CNY',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(entry.value),
                   reason: 'CNY detection failed for: ${entry.key}');
          }
        }
      });

      test('should detect JPY for Japanese context', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'Tokyo sushi ¥1200': 'JPY',
          'Kyoto temple ¥500': 'JPY',
          'Osaka shopping ¥800': 'JPY',
          'Japan travel ¥2000': 'JPY',
          'Japanese restaurant ¥900': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(entry.value),
                   reason: 'JPY detection failed for: ${entry.key}');
          }
        }
      });
    });

    group('Reported Issues - Category Keyword Conflicts', () {
      test('should prefer food category for grocery shopping phrases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'grocery shopping ¥100': 'food',
          'food shopping ¥50': 'food',
          'supermarket shopping ¥75': 'food',
          'restaurant shopping ¥25': 'food',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(entry.value),
                   reason: 'Category conflict resolution failed for: ${entry.key}');
          }
        }
      });

      test('should prefer shopping category for non-food shopping phrases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'clothes shopping ¥100': 'shopping',
          'electronics shopping ¥500': 'shopping',
          'gadget shopping ¥200': 'shopping',
          'fashion shopping ¥150': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(entry.value),
                   reason: 'Shopping category detection failed for: ${entry.key}');
          }
        }
      });
    });

    group('Real-world Integration', () {
      test('should parse real-world transaction examples', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final realWorldCases = SampleTransactions.realWorldExamples;

        for (final entry in realWorldCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;

          final result = await service.parseTransaction(text);

          expect(result, isNotNull);

          // Should either succeed or require user input (not fail completely)
          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should handle real-world case: $text');

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount),
                   reason: 'Amount mismatch for real-world case: $text');
          }
        }
      });
    });

    group('Learning Bypass Functionality', () {
      test('should bypass ML Kit parsing when learned association exists', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'coffee at starbucks';
        const expectedType = TransactionType.expense;
        const expectedCategoryId = 'food';

        // First, learn an association
        await service.learnCategory(text, expectedCategoryId);

        // Parse the same text - should bypass ML Kit and use learned association
        final result = await service.parseTransaction(text);

        expect(result.isSuccess, isTrue);
        expect(result.transaction.type, equals(expectedType));
        expect(result.transaction.categoryId, equals(expectedCategoryId));
        expect(result.transaction.description, equals(text));
      });

      test('should fall back to ML Kit when no learned association exists', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'unknown transaction text';

        // Parse without any learned association
        final result = await service.parseTransaction(text);

        // Should proceed with normal ML Kit parsing
        expect(result, isNotNull);
        // Result can be success, soft fail, or hard fail depending on ML Kit capabilities
      });

      test('should handle learned associations with partial text matching', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const fullText = 'payment to uber technologies inc';
        const partialText = 'uber ride';
        const categoryId = 'transport';

        // Learn with full text
        await service.learnCategory(fullText, categoryId);

        // Should match with partial text
        final result = await service.parseTransaction(partialText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle vendor name extraction in learned associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const originalText = 'Payment to McDonald\'s Restaurant #1234';
        const testText = 'mcdonalds breakfast';
        const categoryId = 'food';

        // Learn with original text
        await service.learnCategory(originalText, categoryId);

        // Should match with vendor name
        final result = await service.parseTransaction(testText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle case insensitive learned associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const learnText = 'Starbucks Coffee';
        const testText = 'STARBUCKS COFFEE';
        const categoryId = 'food';

        // Learn with mixed case
        await service.learnCategory(learnText, categoryId);

        // Should match with different case
        final result = await service.parseTransaction(testText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle learning errors gracefully without breaking parsing', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'test transaction';

        // Corrupt the learned associations storage
        await mockStorage.setString('learned_associations', 'invalid json');

        // Parsing should still work even if learning lookup fails
        final result = await service.parseTransaction(text);
        expect(result, isNotNull);
        // Should fall back to normal ML Kit parsing
      });

      test('should prioritize learned associations over mock entity extractor results', () async {
        // Create a mock that would normally return money entities
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'salary payment \$2000',
          entityText: '\$2000',
          start: 15,
          end: 21,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        const text = 'salary payment \$2000';
        const learnedCategoryId = 'test';

        // Learn an association (should take priority over entity extraction)
        await service.learnCategory(text, learnedCategoryId);

        // Parse - should use learned association, not entity extractor result
        final result = await service.parseTransaction(text);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(learnedCategoryId));
        // The learned association should take priority over entity extraction
      });

      test('should handle multiple learned associations correctly with mock extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final testCases = [
          {'text': 'starbucks coffee \$5.50', 'category': 'food'},
          {'text': 'uber ride \$15.00', 'category': 'transport'},
          {'text': 'amazon purchase \$25.99', 'category': 'shopping'},
        ];

        // Learn multiple associations
        for (final testCase in testCases) {
          await service.learnCategory(
              testCase['text'] as String,
              testCase['category'] as String);
        }

        // Test each association
        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);
          expect(result.isSuccess, isTrue,
              reason: 'Should find learned association for: ${testCase['text']}');
          expect(result.transaction.categoryId, equals(testCase['category']),
              reason: 'Category mismatch for: ${testCase['text']}');
        }
      });
    });

    group('Soft-Fail Logic Validation', () {
      test('should return needsCategory when no category can be determined', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Use text that has amount and type indicators but no recognizable category
        const unknownText = 'spent 25.50 at qwerty zxcvbn';

        final result = await service.parseTransaction(unknownText);

        // Should either need category selection or succeed with 'unknown' placeholder
        if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.isSuccess) {
          // If it succeeds, it should use 'unknown' placeholder, not 'other'
          expect(result.transaction.categoryId, equals('unknown'));
        } else {
          fail('Unexpected parse result: ${result.status}');
        }
      });

      test('should not default to other category for unknown transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        const unknownText = 'spent 42.50 at qwerty zxcvbn';

        final result = await service.parseTransaction(unknownText);

        // Should never return 'other' as category - this was the bug we fixed
        expect(result.transaction.categoryId, isNot(equals('other')));

        // Should either be 'unknown' (placeholder) or require user input
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
        }
      });

      test('should still succeed for learned associations even with soft-fail logic', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        const text = 'spent 30.00 at learned vendor';
        const expectedCategory = 'shopping';

        // Learn an association first
        await service.learnCategory(text, expectedCategory);

        // Parse the same text - should succeed with learned category
        final result = await service.parseTransaction(text);

        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(expectedCategory));
        expect(result.status, equals(ParseStatus.success));
      });

      test('should handle edge cases in soft-fail logic', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          'spent 15.00 at qwerty123',
          'paid 25.50 for zxcvbn',
          'bought asdfgh for 10.00',
        ];

        for (final testText in testCases) {
          final result = await service.parseTransaction(testText);

          // Should never return 'other' category
          expect(result.transaction.categoryId, isNot(equals('other')),
              reason: 'Should not default to other for: $testText');

          // Should either be unknown placeholder or need category selection
          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals('unknown'),
                reason: 'Should use unknown placeholder for: $testText');
          } else {
            expect(result.needsCategorySelection, isTrue,
                reason: 'Should need category selection for: $testText');
          }
        }
      });

      test('should maintain consistency between MlKit and Fallback parser soft-fail behavior', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Test text that will likely fall back to regex parser
        const textWithoutMLKitEntities = 'qwerty transaction 50.00';

        final result = await service.parseTransaction(textWithoutMLKitEntities);

        // Regardless of which parser handles it, should not return 'other'
        expect(result.transaction.categoryId, isNot(equals('other')));

        // Should follow same soft-fail logic
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
        }
      });

      test('should validate soft-fail logic with various transaction types', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': 'spent 20.00 at qwerty place', 'type': TransactionType.expense},
          {'text': 'received 100.00 from zxcvbn source', 'type': TransactionType.income},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          // Should detect correct transaction type
          expect(result.transaction.type, equals(testCase['type']),
              reason: 'Type detection failed for: ${testCase['text']}');

          // Should not default to 'other' category
          expect(result.transaction.categoryId, isNot(equals('other')),
              reason: 'Should not default to other for: ${testCase['text']}');
        }
      });
    });

    group('Currency Bug Regression Tests', () {
      test('should use default currency for learned associations when no explicit currency', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // First, parse a transaction to trigger learning (this would normally require category confirmation)
        // For this test, we'll simulate the learned association scenario directly
        final result1 = await service.parseTransaction('1000 clothes');

        // The transaction should use VND currency, not USD
        expect(result1.transaction.currencyCode, equals('VND'),
            reason: 'Should use default currency VND, not hardcoded USD');
      });

      test('should use explicit currency symbols even with default currency set', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // Parse transaction with explicit USD symbol
        final result = await service.parseTransaction('\$1000 clothes');

        // Should use USD from symbol, not default VND
        expect(result.transaction.currencyCode, equals('USD'),
            reason: 'Explicit currency symbol should override default currency');
      });

      test('should use explicit currency codes even with default currency set', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // Parse transaction with explicit EUR code
        final result = await service.parseTransaction('1000 EUR clothes');

        // Should use EUR from code, not default VND
        expect(result.transaction.currencyCode, equals('EUR'),
            reason: 'Explicit currency code should override default currency');
      });
    });

    group('Number Abbreviation Support Tests', () {
      test('should parse basic abbreviations correctly', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100k food', 'expectedAmount': 100000.0},
          {'text': '2.5M salary', 'expectedAmount': 2500000.0},
          {'text': '1.2B investment', 'expectedAmount': 1200000000.0},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing failed for: ${testCase['text']}');
        }
      });

      test('should handle case variations in abbreviations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100K food', 'expectedAmount': 100000.0},
          {'text': '2m salary', 'expectedAmount': 2000000.0},
          {'text': '1.5b investment', 'expectedAmount': 1500000000.0},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Case-insensitive parsing failed for: ${testCase['text']}');
        }
      });

      test('should handle abbreviations with currency symbols', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '\$100k shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '€2.5M bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '₫1.5k transport', 'expectedAmount': 1500.0, 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing with currency failed for: ${testCase['text']}');
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency detection failed for: ${testCase['text']}');
        }
      });

      test('should handle abbreviations with currency codes', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100k USD shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '2.5M EUR bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '1.2B VND investment', 'expectedAmount': 1200000000.0, 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing with currency code failed for: ${testCase['text']}');
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency code detection failed for: ${testCase['text']}');
        }
      });

      test('should handle thousands separators with abbreviations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '1,500k shopping', 'expectedAmount': 1500000.0},
          {'text': '2,500.50M investment', 'expectedAmount': 2500500000.0},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Thousands separator parsing failed for: ${testCase['text']}');
        }
      });

      test('should maintain backward compatibility with non-abbreviated amounts', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '\$100.50 food', 'expectedAmount': 100.50},
          {'text': '1,500 euros shopping', 'expectedAmount': 1500.0},
          {'text': '250.75 transport', 'expectedAmount': 250.75},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Backward compatibility failed for: ${testCase['text']}');
        }
      });
    });

    group('Embedded Numbers vs Abbreviations', () {
      test('should parse "com trua tai Lux68 2m" as 2,000,000 not 68', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateLux68Scenario();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('com trua tai Lux68 2m');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(2000000.0),
            reason: 'Should parse "2m" as 2 million, not "68" from Lux68');
      });

      test('should parse "dinner at Cafe123 500k" as 500,000 not 123', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'dinner at Cafe123 500k',
          embeddedNumber: '123',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('dinner at Cafe123 500k');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(500000.0),
            reason: 'Should parse "500k" as 500,000, not "123" from Cafe123');
      });

      test('should parse "shopping Mall456 1.5M" as 1,500,000 not 456', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'shopping Mall456 1.5M',
          embeddedNumber: '456',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shopping Mall456 1.5M');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(1500000.0),
            reason: 'Should parse "1.5M" as 1.5 million, not "456" from Mall456');
      });
    });

    group('Vendor Name Detection', () {
      setUp(() {
        // Ensure fresh singleton for vendor name detection tests
        MlKitParserService.resetInstance();
      });

      test('should ignore Hotel789 embedded numbers when larger amounts exist', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'Hotel789 1000k stay',
          embeddedNumber: '789',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Hotel789 1000k stay');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(1000000.0),
            reason: 'Should parse "1000k" as 1 million, ignoring embedded "789"');
      });

      test('should accept \$789 even if embedded when has currency context', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'Hotel789 \$789 fee',
          embeddedNumber: '\$789',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Hotel789 \$789 fee');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(789.0),
            reason: 'Should accept \$789 as valid amount due to currency context');
      });

      test('should choose largest reasonable amount with multiple embedded numbers', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '123', 'start': 4, 'end': 7},
            {'text': '456', 'start': 12, 'end': 15},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop123 Mall456 2.5M purchase');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(2500000.0),
            reason: 'Should choose "2.5M" over embedded numbers 123 and 456');
      });
    });

    group('Mock Entity Extractor Tests', () {
      test('should handle Lux68 scenario with mock returning specific ML Kit results', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateLux68Scenario();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('com trua tai Lux68 2m');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(2000000.0),
            reason: 'Mock should simulate ML Kit finding "68" but parser should choose "2m"');
      });

      test('should verify selection algorithm with controlled inputs', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '50', 'start': 10, 'end': 12},
            {'text': '100', 'start': 17, 'end': 20},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Restaurant50 Hotel100 5M total');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(5000000.0),
            reason: 'Should select "5M" over smaller embedded amounts');
      });

      test('should verify fallback behavior when ML Kit fails', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError('Simulated ML Kit failure');
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 2.5M on equipment');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(2500000.0),
            reason: 'Should fall back to regex parsing when ML Kit fails');
      });
    });

    group('Amount Ambiguity Detection', () {
      setUp(() {
        // Ensure fresh singleton for ambiguity detection tests
        MlKitParserService.resetInstance();
      });

      test('should trigger amount confirmation for multiple non-embedded numbers', () async {
        // Create mock that simulates ML Kit finding multiple non-embedded money entities
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'Paid 100 and 200 for shopping',
          moneyEntities: [
            {'text': '100', 'start': 5, 'end': 8},
            {'text': '200', 'start': 13, 'end': 16},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Paid 100 and 200 for shopping');

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation));
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts!.length, equals(2));
        expect(result.candidateAmounts, containsAll([100.0, 200.0]));
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateTexts!.length, equals(2));
      });

      test('should trigger amount confirmation for the exact user-reported scenario', () async {
        // Test the exact scenario: "An com taij lux73 100"
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'An com taij lux73 100',
          moneyEntities: [
            {'text': '73', 'start': 15, 'end': 17}, // Embedded in "lux73"
            {'text': '100', 'start': 18, 'end': 21}, // Standalone amount
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('An com taij lux73 100');

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation));
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts!.length, equals(2));
        expect(result.candidateAmounts, containsAll([73.0, 100.0]));
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateTexts, containsAll(['73', '100']));
      });

      test('should NOT trigger confirmation when embedded numbers are filtered out', () async {
        // Test scenario where smart selection should work without confirmation
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Dinner at lux68 2m',
          entityText: '68',
          start: 13, // Position of "68" in "lux68"
          end: 15,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Dinner at lux68 2m');

        expect(result, isNotNull);
        // Should NOT trigger amount confirmation because 68 is embedded and 2m is found by AmountUtils
        expect(result.status, isNot(equals(ParseStatus.needsAmountConfirmation)));
        expect(result.transaction.amount, equals(2000000.0)); // Should choose 2m
      });

      test('should format candidate amounts correctly for display', () async {
        // Test with various amount formats including abbreviations
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'Shopping 1000 or 2k or 3.5m',
          moneyEntities: [
            {'text': '1000', 'start': 9, 'end': 13},
            {'text': '2k', 'start': 17, 'end': 19},
            {'text': '3.5m', 'start': 23, 'end': 27},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shopping 1000 or 2k or 3.5m');

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation));
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);

        // Check that amounts are properly formatted for display
        final expectedAmounts = [1000.0, 2000.0, 3500000.0];
        final expectedTexts = ['1000', '2k', '3.5m'];

        for (int i = 0; i < expectedAmounts.length; i++) {
          expect(result.candidateAmounts, contains(expectedAmounts[i]));
        }

        // Verify that text formatting is reasonable (exact format may vary)
        expect(result.candidateTexts!.length, equals(expectedAmounts.length));
      });

      test('should remove duplicate amounts from candidates', () async {
        // Test scenario with duplicate amounts
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'Cost 100 dollars or 100 USD',
          moneyEntities: [
            {'text': '100', 'start': 5, 'end': 8},
            {'text': '100', 'start': 19, 'end': 22},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Cost 100 dollars or 100 USD');

        expect(result, isNotNull);
        // Should NOT trigger confirmation because duplicates are removed, leaving only one candidate
        expect(result.status, isNot(equals(ParseStatus.needsAmountConfirmation)));
        expect(result.transaction.amount, equals(100.0));
      });

      test('should handle edge case with only embedded numbers', () async {
        // Test scenario where all ML Kit candidates are embedded
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'Dinner at lux68 mall73',
          moneyEntities: [
            {'text': '68', 'start': 13, 'end': 15}, // Embedded in "lux68"
            {'text': '73', 'start': 20, 'end': 22}, // Embedded in "mall73"
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Dinner at lux68 mall73');

        expect(result, isNotNull);
        // Should NOT trigger confirmation because all candidates are embedded
        expect(result.status, isNot(equals(ParseStatus.needsAmountConfirmation)));
        // Should fall back to regex parsing or use fallback logic
      });

      test('should trigger amount confirmation for embedded vs standalone numbers', () async {
        // Test the specific user-reported scenario: "an com tai lux69 100"
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '69', 'start': 14, 'end': 16},  // embedded in "lux69"
            {'text': '100', 'start': 17, 'end': 20}, // standalone "100"
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('an com tai lux69 100');

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation),
            reason: 'Should trigger amount confirmation for embedded vs standalone numbers');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts, contains(69.0),
            reason: 'Should include embedded number 69 as candidate');
        expect(result.candidateAmounts, contains(100.0),
            reason: 'Should include standalone number 100 as candidate');
        expect(result.candidateAmounts!.length, equals(2),
            reason: 'Should have exactly 2 candidates');
      });

      test('should trigger amount confirmation for restaurant45 mall 200 pattern', () async {
        final text = 'restaurant45 mall 200';
        final pos45Start = text.indexOf('45');
        final pos45End = pos45Start + 2;
        final pos200Start = text.indexOf('200');
        final pos200End = pos200Start + 3;

        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '45', 'start': pos45Start, 'end': pos45End},
            {'text': '200', 'start': pos200Start, 'end': pos200End},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation),
            reason: 'Should trigger amount confirmation for embedded vs standalone numbers');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts, contains(45.0));
        expect(result.candidateAmounts, contains(200.0));
        expect(result.candidateAmounts!.length, equals(2));
      });

      test('should trigger amount confirmation for shop123 total 500 pattern', () async {
        final text = 'shop123 total 500';
        final pos123Start = text.indexOf('123');
        final pos123End = pos123Start + 3;
        final pos500Start = text.indexOf('500');
        final pos500End = pos500Start + 3;

        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '123', 'start': pos123Start, 'end': pos123End},
            {'text': '500', 'start': pos500Start, 'end': pos500End},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation),
            reason: 'Should trigger amount confirmation for embedded vs standalone numbers');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts, contains(123.0));
        expect(result.candidateAmounts, contains(500.0));
        expect(result.candidateAmounts!.length, equals(2));
      });

      test('should trigger amount confirmation for cafe88 bill 150 pattern', () async {
        final text = 'cafe88 bill 150';
        final pos88Start = text.indexOf('88');
        final pos88End = pos88Start + 2;
        final pos150Start = text.indexOf('150');
        final pos150End = pos150Start + 3;

        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '88', 'start': pos88Start, 'end': pos88End},
            {'text': '150', 'start': pos150Start, 'end': pos150End},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation),
            reason: 'Should trigger amount confirmation for embedded vs standalone numbers');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts, contains(88.0));
        expect(result.candidateAmounts, contains(150.0));
        expect(result.candidateAmounts!.length, equals(2));
      });
    });

    group('Regression Tests', () {
      test('should maintain simple amounts like \$100 food functionality', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$100 on food',
          entityText: '\$100',
          start: 6,
          end: 10,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$100 on food');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100.0));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should maintain currency detection accuracy', () async {
        final testCases = [
          {'text': 'Spent €50 on dinner', 'expectedCurrency': 'EUR'},
          {'text': 'Paid £25 for taxi', 'expectedCurrency': 'GBP'},
          {'text': 'Cost ¥1000 for shopping', 'expectedCurrency': 'JPY'},
        ];

        for (final testCase in testCases) {
          final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
          final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result, isNotNull);
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency detection failed for: ${testCase['text']}');
        }
      });

      test('should not affect learned associations', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        const text = 'Starbucks coffee \$5.50';
        const categoryId = 'food';

        // Learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text again - should use learned association
        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });
    });

    group('Enhanced Vendor Name Detection Unit Tests', () {
      test('should use position-based detection instead of indexOf', () async {
        // Test the exact scenario where indexOf would fail but position-based detection works
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '70',
          start: 16, // Position of "70" in "com trua tai lux70 100k" - "70" is at positions 16-18
          end: 18,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('com trua tai lux70 100k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100000.0),
            reason: 'Position-based detection should correctly identify embedded number and choose 100k');
      });

      test('should handle multiple occurrences of same number correctly', () async {
        // Test scenario where same number appears multiple times
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '100',
          start: 4, // First occurrence of "100" in "Shop100 Mall100 500k" - "100" is at positions 4-7
          end: 7,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop100 Mall100 500k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(500000.0),
            reason: 'Should choose 500k over embedded 100, even with multiple occurrences');
      });

      test('should handle case-insensitive vendor name detection', () async {
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '70',
          start: 16, // Position in "com trua tai Lux70 100k" - "70" is at positions 16-18
          end: 18,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('com trua tai Lux70 100k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100000.0),
            reason: 'Should handle case variations in vendor names correctly');
      });

      test('should prefer abbreviations over embedded numbers in selection logic', () async {
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '999',
          start: 5, // Position of "999" in "Store999 1k"
          end: 8,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Store999 1k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(1000.0),
            reason: 'Enhanced selection logic should always prefer abbreviations over embedded numbers');
      });

      test('should handle 20x threshold logic correctly', () async {
        // Test edge case where amounts are close to 20x threshold
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '80',
          start: 5, // Position of "80" in "Hotel80 100"
          end: 7,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Hotel80 100');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100.0),
            reason: 'Should choose 100 over 80 when below 20x threshold (100/80 = 1.25x)');
      });

      test('should handle exact 20x threshold boundary', () async {
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '5',
          start: 5, // Position of "5" in "Store5 100"
          end: 6,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Store5 100');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100.0),
            reason: 'Should choose 100 over 5 at exactly 20x threshold (100/5 = 20x)');
      });

      test('should preserve currency when selecting AmountUtils result', () async {
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '50',
          start: 4, // Position of "50" in "Cafe50 100k vnd"
          end: 6,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Cafe50 100k vnd');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100000.0),
            reason: 'Should choose 100k over embedded 50');
        expect(result.transaction.currencyCode, equals('VND'),
            reason: 'Should preserve VND currency when selecting AmountUtils result');
      });
    });

    group('Enhanced Amount Selection Logic Unit Tests', () {
      test('should log selection decisions for debugging', () async {
        // This test verifies that the enhanced logging is working
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '123',
          start: 4, // Position of "123" in "Shop123 500k"
          end: 7,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop123 500k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(500000.0),
            reason: 'Should choose 500k and log the decision process');
      });

      test('should handle multiple ML Kit entities correctly', () async {
        final mockExtractor = MockEntityExtractorFactory.createMultipleEmbeddedScenario();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop123 Mall456 100k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100000.0),
            reason: 'Should choose 100k over multiple embedded numbers 123 and 456');
      });

      test('should handle currency symbol preservation in complex scenarios', () async {
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '25',
          start: 4, // Position of "25" in "Shop25 \$100k"
          end: 6,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop25 \$100k');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100000.0),
            reason: 'Should choose \$100k over embedded 25');
        expect(result.transaction.currencyCode, equals('USD'),
            reason: 'Should preserve USD currency from symbol');
      });

      test('should handle fallback to AmountUtils when ML Kit fails', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // No ML Kit results
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Restaurant 100k dinner');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100000.0),
            reason: 'Should fall back to AmountUtils and parse 100k correctly');
      });

      test('should handle edge case with very large embedded numbers', () async {
        final mockExtractor = MockEntityExtractorFactory.createExactPositionScenario(
          entityText: '999999',
          start: 4, // Position of "999999" in "Shop999999 1m"
          end: 10,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop999999 1m');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(1000000.0),
            reason: 'Should choose 1m over large embedded number 999999');
      });
    });

    group('Multiple Number Ambiguity Detection Unit Tests', () {
      setUp(() {
        // Ensure fresh singleton for ambiguity detection tests
        MlKitParserService.resetInstance();
      });

      test('should detect ambiguity between embedded and standalone amounts', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '69', 'start': 3, 'end': 5}, // Embedded in 'lux69'
            {'text': '100', 'start': 7, 'end': 10}, // Standalone amount
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('lux69 100');

        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.needsAmountConfirmation),
            reason: 'Should trigger amount confirmation for ambiguous amounts');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateAmounts!.length, equals(2));
        expect(result.candidateAmounts, containsAll([69.0, 100.0]));
      });

      test('should not bypass ambiguity detection when ML Kit is empty', () async {
        // This tests the specific fix for the early return bypass bug
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('lux69 100');

        expect(result, isNotNull);
        // Should not bypass ambiguity detection even when ML Kit returns empty
        expect([ParseStatus.success, ParseStatus.needsAmountConfirmation, ParseStatus.needsCategory],
               contains(result.status));

        if (result.status == ParseStatus.success || result.status == ParseStatus.needsCategory) {
          // Should parse the standalone amount correctly
          expect(result.transaction.amount, equals(100.0));
        }
      });

      test('should consolidate candidates from multiple sources', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '69', 'start': 3, 'end': 5}, // From ML Kit
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        // This scenario has ML Kit candidate (69) and AmountUtils candidate (100)
        final result = await service.parseTransaction('lux69 100');

        expect(result, isNotNull);

        if (result.status == ParseStatus.needsAmountConfirmation) {
          expect(result.candidateAmounts, isNotNull);
          expect(result.candidateAmounts!.length, greaterThan(1));
          expect(result.candidateAmounts, anyOf(
            containsAll([69.0, 100.0]),
            contains(100.0) // At minimum should contain the standalone amount
          ));
        }
      });

      test('should handle currency preservation in ambiguity scenarios', () async {
        await mockStorage.saveDefaultCurrency('VND');

        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '69', 'start': 3, 'end': 5}, // Embedded in 'lux69'
            {'text': '100', 'start': 7, 'end': 10}, // Part of '100k vnd'
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('lux69 100k vnd');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('VND'),
            reason: 'Should preserve VND currency in ambiguity result');

        if (result.status == ParseStatus.needsAmountConfirmation) {
          expect(result.candidateAmounts, isNotNull);
          expect(result.candidateAmounts!.length, greaterThan(1));
          expect(result.candidateAmounts, anyOf(
            containsAll([69.0, 100000.0]),
            contains(100000.0)
          ));
        }
      });

      test('should not trigger ambiguity for single clear candidate', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '100', 'start': 11, 'end': 14}, // Part of '100k'
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('restaurant 100k');

        expect(result, isNotNull);
        expect([ParseStatus.success, ParseStatus.needsCategory], contains(result.status),
            reason: 'Should not trigger ambiguity for single clear candidate');
        expect(result.transaction.amount, equals(100000.0));
      });

      test('should handle 20x threshold boundary correctly', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '15', 'start': 5, 'end': 7}, // Embedded in 'shop15'
            {'text': '300', 'start': 14, 'end': 17}, // Standalone amount (15 * 20 = 300)
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop15 total 300');

        expect(result, isNotNull);
        // At exactly 20x threshold, should trigger ambiguity
        if (result.status == ParseStatus.needsAmountConfirmation) {
          expect(result.candidateAmounts, containsAll([15.0, 300.0]));
        }
      });

      test('should not trigger ambiguity beyond 20x threshold', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '10', 'start': 5, 'end': 7}, // Embedded in 'shop10'
            {'text': '250', 'start': 14, 'end': 17}, // Standalone amount (10 * 25 = 250)
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop10 total 250');

        expect(result, isNotNull);
        // Beyond 20x threshold, should choose the larger amount without ambiguity
        expect([ParseStatus.success, ParseStatus.needsCategory], contains(result.status));
        expect(result.transaction.amount, equals(250.0));
      });

      test('should handle multiple embedded numbers with one standalone', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '123', 'start': 4, 'end': 7}, // Embedded in 'shop123'
            {'text': '456', 'start': 12, 'end': 15}, // Embedded in 'mall456'
            {'text': '2500000', 'start': 17, 'end': 24}, // Standalone '2.5M'
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop123 mall456 2.5M');

        expect(result, isNotNull);
        // Should choose the large standalone amount over embedded numbers
        expect([ParseStatus.success, ParseStatus.needsCategory], contains(result.status));
        expect(result.transaction.amount, equals(2500000.0));
      });

      test('should verify fix prevents early return bypass', () async {
        // This test specifically verifies the fix for the bug described in the plan
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // ML Kit returns empty
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        // Before the fix: this would trigger early return and bypass ambiguity detection
        // After the fix: this should properly consolidate candidates and check for ambiguity
        final result = await service.parseTransaction('lux69 100');

        expect(result, isNotNull);
        // The fix ensures we don't bypass ambiguity detection
        expect([ParseStatus.success, ParseStatus.needsAmountConfirmation, ParseStatus.needsCategory],
               contains(result.status));

        // Should not have bypassed the logic - either success with correct amount or proper ambiguity handling
        if (result.status == ParseStatus.success || result.status == ParseStatus.needsCategory) {
          expect(result.transaction.amount, equals(100.0),
              reason: 'Should parse standalone amount correctly without bypass');
        }
      });

      test('should handle completeTransaction after ambiguity detection', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '69', 'start': 3, 'end': 5},
            {'text': '100', 'start': 7, 'end': 10},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        // First, trigger ambiguity
        final initialResult = await service.parseTransaction('lux69 100');

        if (initialResult.status == ParseStatus.needsAmountConfirmation) {
          // User selects amount
          final confirmedResult = await service.completeTransaction('lux69 100', 100.0);

          expect(confirmedResult, isNotNull);
          expect([ParseStatus.success, ParseStatus.needsCategory], contains(confirmedResult.status));
          expect(confirmedResult.transaction.amount, equals(100.0));
        }
      });
    });
  });
}
