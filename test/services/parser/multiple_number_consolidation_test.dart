import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/mlkit_parser_service.dart';
import '../../../lib/models/amount_candidate.dart';
import '../../../lib/models/transaction_model.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_entity_extractor.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Multiple Number Consolidation Tests', () {
    late MockStorageService mockStorage;
    late MlKitParserService service;

    setUp(() async {
      MlKitParserService.resetInstance();
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      MlKitParserService.resetInstance();
    });

    group('Consolidation Logic', () {
      test('should consolidate ML Kit and raw finder candidates without duplicates', () async {
        // Create mock that returns ML Kit entity for "100"
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent 100 at shop45',
          entityText: '100',
          start: 6,
          end: 9,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 100 at shop45');

        expect(result, isNotNull);
        // Should find both "100" (ML Kit + raw finder) and "45" (raw finder only)
        // But "100" should not be duplicated in consolidation
        expect(result.isSuccess || result.requiresUserInput, isTrue);
      });

      test('should handle case where ML Kit finds nothing but raw finder finds numbers', () async {
        // Create mock that returns no entities
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop123 total 500k');

        expect(result, isNotNull);
        // Should find both "123" and "500k" via raw number finder
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        // Should prefer the larger amount (500k = 500000)
        expect(result.transaction.amount, equals(500000.0));
      });

      test('should handle case where ML Kit finds multiple entities', () async {
        // Create mock that returns multiple money entities
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: 'Transfer 100 USD to account, fee 5 USD',
          moneyEntities: [
            {'text': '100 USD', 'start': 9, 'end': 16},
            {'text': '5 USD', 'start': 33, 'end': 38},
          ],
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Transfer 100 USD to account, fee 5 USD');

        expect(result, isNotNull);
        // Should trigger ambiguity detection due to multiple amounts
        expect(result.requiresUserInput, isTrue);
        expect(result.needsAmountConfirmation, isTrue);
      });

      test('should sort consolidated candidates by position', () async {
        // Create mock that finds entity at later position
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'shop45 paid 100k',
          entityText: '100k',
          start: 11,
          end: 15,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop45 paid 100k');

        expect(result, isNotNull);
        // Should find both "45" (position 4-6) and "100k" (position 11-15)
        // Final result should be sorted by position
        expect(result.isSuccess || result.requiresUserInput, isTrue);
      });
    });

    group('Ambiguity Detection Logic', () {
      test('should not trigger ambiguity with single candidate', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent 100 on coffee',
          entityText: '100',
          start: 6,
          end: 9,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 100 on coffee');

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.requiresUserInput, isFalse);
        expect(result.transaction.amount, equals(100.0));
      });

      test('should trigger ambiguity with multiple non-embedded candidates', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Transfer 100 USD, fee 25 USD');

        expect(result, isNotNull);
        expect(result.requiresUserInput, isTrue);
        expect(result.needsAmountConfirmation, isTrue);
        // Should offer both amounts for confirmation
        expect(result.candidateAmounts, hasLength(2));
        expect(result.candidateAmounts, containsAll([100.0, 25.0]));
      });

      test('should filter out embedded numbers when non-embedded alternatives exist', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('hotel789 room 150 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should choose 150 USD and ignore embedded "789"
        expect(result.transaction.amount, equals(150.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should include embedded numbers when no non-embedded alternatives exist', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('hotel789 booking');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should use embedded "789" as the only available amount
        expect(result.transaction.amount, equals(789.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should prefer non-embedded numbers over embedded ones', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop45 total 200 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should prefer non-embedded 200 USD over embedded 45
        expect(result.transaction.amount, equals(200.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should exclude embedded numbers beyond 1 order of magnitude', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shop45 total 1000 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should choose non-embedded 1000 USD and ignore embedded "45"
        expect(result.transaction.amount, equals(1000.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should remove duplicate amounts in ambiguity detection', () async {
        // Create scenario where ML Kit and raw finder both find the same amount
        final mockExtractor = MockEntityExtractorFactory.createAmountConfirmationScenario(
          fullText: '100 USD transfer, fee 100 USD',
          moneyEntities: [
            {'text': '100 USD', 'start': 0, 'end': 7},
            {'text': '100 USD', 'start': 22, 'end': 29},
          ],
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('100 USD transfer, fee 100 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should deduplicate and treat as single amount
        expect(result.transaction.amount, equals(100.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });
    });

    group('Best Candidate Selection Logic', () {
      test('should return single candidate when only one exists', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent 100 on coffee',
          entityText: '100',
          start: 6,
          end: 9,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 100 on coffee');

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.amount, equals(100.0));
      });

      test('should prefer non-embedded candidates over embedded ones', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('hotel789 room 150 USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should prefer non-embedded "150" over embedded "789"
        expect(result.transaction.amount, equals(150.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });

      test('should trigger ambiguity for multiple non-embedded candidates', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Payment 100 or 2k USD');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should trigger ambiguity between "100" and "2k" (2000)
        expect(result.needsAmountConfirmation, isTrue);
        expect(result.candidateAmounts, containsAll([100.0, 2000.0]));
      });

      test('should fall back to first candidate when all are embedded', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('hotel789 room456');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should use first embedded candidate "789" (position comes first)
        expect(result.transaction.amount, equals(789.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });
    });

    group('PRD Core Scenarios', () {
      test('should handle "dinner at lux69 100k vnd" correctly', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('dinner at lux69 100k vnd');

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        // Should choose "100k" (100000) over embedded "69"
        expect(result.transaction.amount, equals(100000.0));
        expect(result.transaction.currencyCode, equals('VND'));
      });

      test('should handle "restaurant45 mall 200 USD" correctly', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('restaurant45 mall 200 USD');

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        // Should choose "200" over embedded "45"
        expect(result.transaction.amount, equals(200.0));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should handle ML Kit + raw finder combination', () async {
        // ML Kit finds one amount, raw finder finds additional amounts
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Transfer 500 to shop123',
          entityText: '500',
          start: 9,
          end: 12,
        );
        service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Transfer 500 to shop123');

        expect(result, isNotNull);
        expect(result.hasError, isFalse);
        // Should find both "500" (ML Kit + raw) and "123" (raw only)
        // Should prefer non-embedded "500" over embedded "123"
        expect(result.transaction.amount, equals(500.0));
        // Should not trigger amount ambiguity (may need type confirmation)
        expect(result.needsAmountConfirmation, isFalse);
      });
    });
  });
}
