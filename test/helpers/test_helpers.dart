import 'package:uuid/uuid.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/models/parse_result.dart';

/// Helper functions for testing transaction parsing and models
class TestHelpers {
  static const Uuid _uuid = Uuid();

  /// Create a test transaction with default values
  static Transaction createTestTransaction({
    String? id,
    double amount = 100.0,
    TransactionType type = TransactionType.expense,
    String categoryId = 'test-category',
    DateTime? date,
    String description = 'Test transaction',
    List<String> tags = const [],
    String currencyCode = 'USD',
  }) {
    return Transaction(
      id: id ?? _uuid.v4(),
      amount: amount,
      type: type,
      categoryId: categoryId,
      date: date ?? DateTime.now(),
      description: description,
      tags: tags,
      currencyCode: currencyCode,
    );
  }

  /// Create a test category with default values
  static Category createTestCategory({
    String? id,
    String name = 'Test Category',
    String icon = '💳',
    int colorValue = 0xFF2196F3,
    TransactionType type = TransactionType.expense,
  }) {
    return Category(
      id: id ?? _uuid.v4(),
      name: name,
      icon: icon,
      colorValue: colorValue,
      type: type,
    );
  }

  /// Create a ParseResult.success for testing
  static ParseResult createSuccessParseResult({Transaction? transaction}) {
    return ParseResult.success(
      transaction ?? createTestTransaction(),
    );
  }

  /// Create a ParseResult.needsCategory for testing
  static ParseResult createNeedsCategoryParseResult({Transaction? transaction}) {
    return ParseResult.needsCategory(
      transaction ?? createTestTransaction(),
    );
  }

  /// Create a ParseResult.needsType for testing
  static ParseResult createNeedsTypeParseResult({Transaction? transaction}) {
    return ParseResult.needsType(
      transaction ?? createTestTransaction(),
    );
  }

  /// Create a ParseResult.failed for testing
  static ParseResult createFailedParseResult({
    Transaction? transaction,
    String error = 'Test error',
  }) {
    return ParseResult.failed(
      transaction ?? createTestTransaction(),
      error,
    );
  }

  /// Generate various test transaction scenarios
  static List<Transaction> getTestTransactionScenarios() {
    return [
      // Basic expense
      createTestTransaction(
        amount: 25.50,
        type: TransactionType.expense,
        description: 'Coffee at Starbucks',
        currencyCode: 'USD',
      ),
      
      // Income transaction
      createTestTransaction(
        amount: 2500.00,
        type: TransactionType.income,
        description: 'Salary payment',
        currencyCode: 'USD',
        tags: ['salary', 'work'],
      ),
      
      // Loan transaction
      createTestTransaction(
        amount: 500.00,
        type: TransactionType.loan,
        description: 'Loan to friend',
        currencyCode: 'USD',
        tags: ['personal', 'loan'],
      ),
      
      // Different currency
      createTestTransaction(
        amount: 150.75,
        type: TransactionType.expense,
        description: 'Dinner in Paris',
        currencyCode: 'EUR',
        tags: ['food', 'travel'],
      ),
      
      // Very small amount
      createTestTransaction(
        amount: 0.01,
        type: TransactionType.expense,
        description: 'Rounding difference',
        currencyCode: 'USD',
      ),
      
      // Large amount
      createTestTransaction(
        amount: 99999.99,
        type: TransactionType.expense,
        description: 'Car purchase',
        currencyCode: 'USD',
        tags: ['vehicle', 'major'],
      ),
      
      // Zero decimal currency
      createTestTransaction(
        amount: 1000.0,
        type: TransactionType.expense,
        description: 'Sushi dinner',
        currencyCode: 'JPY',
        tags: ['food', 'restaurant'],
      ),
    ];
  }

  /// Generate test transaction texts for parsing
  static List<String> getTestTransactionTexts() {
    return [
      'Spent \$25.50 on coffee',
      'Received €150.75 salary',
      'Paid ¥1000 for dinner',
      'Loan £45.99 to friend',
      'Income \$2500 from freelance work',
      'Expense of ₹2500.50 for groceries',
      'Bought coffee for \$5.00 #coffee #morning',
      'Dinner at restaurant \$45.50 #food #dining',
      'Gas bill payment \$123.45 #utilities #bills',
      'Grocery shopping €85.30 #food #groceries',
      'Movie tickets \$24.00 #entertainment #movies',
      'Pharmacy \$15.75 #health #pharmacy',
      'Bus fare \$2.50 #transport #public',
      'Lunch \$12.99 #food #lunch',
      'Book purchase \$19.95 #books #education',
      'ATM withdrawal \$100.00 #cash #atm',
    ];
  }

  /// Generate edge case transaction texts
  static List<String> getEdgeCaseTransactionTexts() {
    return [
      '', // Empty string
      '   ', // Whitespace only
      'No money mentioned here',
      'spent money but no amount',
      '\$0.00 free coffee',
      'negative \$-50.00 refund',
      'Multiple amounts \$10 and \$20',
      'Invalid currency XYZ123.45',
      'Very long description that goes on and on with lots of details about a transaction that cost \$5.00',
      'Special chars !@#\$%^&*() \$10.00',
      'Unicode test 🍕💰 \$15.50',
      'Mixed languages café \$12.00 ресторан',
    ];
  }

  /// Create test categories for different transaction types
  static List<Category> getTestCategories() {
    return [
      createTestCategory(
        id: 'food',
        name: 'Food & Dining',
        icon: '🍽️',
        type: TransactionType.expense,
      ),
      createTestCategory(
        id: 'transport',
        name: 'Transportation',
        icon: '🚗',
        type: TransactionType.expense,
      ),
      createTestCategory(
        id: 'entertainment',
        name: 'Entertainment',
        icon: '🎬',
        type: TransactionType.expense,
      ),
      createTestCategory(
        id: 'salary',
        name: 'Salary',
        icon: '💼',
        type: TransactionType.income,
      ),
      createTestCategory(
        id: 'freelance',
        name: 'Freelance',
        icon: '💻',
        type: TransactionType.income,
      ),
      createTestCategory(
        id: 'personal-loan',
        name: 'Personal Loan',
        icon: '🤝',
        type: TransactionType.loan,
      ),
    ];
  }
}

/// Custom matchers for testing ParseResult objects
class ParseResultMatchers {

  /// Check if ParseResult is successful (complete success, no user input needed)
  static bool isSuccess(ParseResult result) {
    return result.status == ParseStatus.success &&
           result.isSuccess &&
           !result.needsCategorySelection &&
           !result.needsTypeSelection &&
           result.error == null;
  }

  /// Check if ParseResult needs category selection
  static bool needsCategory(ParseResult result) {
    return result.status == ParseStatus.needsCategory &&
           result.needsCategorySelection &&
           !result.needsTypeSelection &&
           !result.isSuccess && // needsCategory is not considered successful
           result.error == null;
  }

  /// Check if ParseResult needs type selection
  static bool needsType(ParseResult result) {
    return result.status == ParseStatus.needsType &&
           result.needsTypeSelection &&
           !result.needsCategorySelection &&
           !result.isSuccess && // needsType is not considered successful
           result.error == null;
  }

  /// Check if ParseResult has failed
  static bool isFailed(ParseResult result) {
    return result.status == ParseStatus.failed &&
           result.hasError &&
           result.error != null;
  }

  /// Check if ParseResult requires user input
  static bool requiresUserInput(ParseResult result) {
    return result.requiresUserInput;
  }

  /// Validate ParseResult structure
  static bool isValidParseResult(ParseResult result) {
    // Must have consistent state
    if (result.hasError && result.error == null) return false;
    if (!result.hasError && result.error != null) return false;

    // Status-specific validations
    switch (result.status) {
      case ParseStatus.success:
        return result.isSuccess &&
               !result.needsCategorySelection &&
               !result.needsTypeSelection &&
               !result.requiresUserInput &&
               result.error == null;

      case ParseStatus.needsCategory:
        return !result.isSuccess && // needsCategory is not considered successful
               result.needsCategorySelection &&
               !result.needsTypeSelection &&
               result.requiresUserInput &&
               result.error == null;

      case ParseStatus.needsType:
        return !result.isSuccess && // needsType is not considered successful
               !result.needsCategorySelection &&
               result.needsTypeSelection &&
               result.requiresUserInput &&
               result.error == null;

      case ParseStatus.needsAmountConfirmation:
        return !result.isSuccess && // needsAmountConfirmation is not considered successful
               !result.needsCategorySelection &&
               !result.needsTypeSelection &&
               result.needsAmountConfirmation &&
               result.requiresUserInput &&
               result.error == null;

      case ParseStatus.failed:
        return !result.isSuccess &&
               !result.needsCategorySelection &&
               !result.needsTypeSelection &&
               !result.requiresUserInput && // Failed status does not require user input
               result.hasError &&
               result.error != null;
    }
  }
}

/// Assertion helpers for testing
class TestAssertions {
  
  /// Assert that two transactions are equal (excluding ID if not provided)
  static bool transactionsEqual(Transaction a, Transaction b, {bool ignoreId = false}) {
    if (!ignoreId && a.id != b.id) return false;
    if (a.amount != b.amount) return false;
    if (a.type != b.type) return false;
    if (a.categoryId != b.categoryId) return false;
    if (a.description != b.description) return false;
    if (a.currencyCode != b.currencyCode) return false;
    if (a.tags.length != b.tags.length) return false;
    
    for (int i = 0; i < a.tags.length; i++) {
      if (a.tags[i] != b.tags[i]) return false;
    }
    
    // Compare dates with some tolerance (1 second)
    final timeDiff = a.date.difference(b.date).abs();
    if (timeDiff.inSeconds > 1) return false;
    
    return true;
  }
  
  /// Assert that two categories are equal
  static bool categoriesEqual(Category a, Category b) {
    return a.id == b.id &&
           a.name == b.name &&
           a.icon == b.icon &&
           a.colorValue == b.colorValue &&
           a.type == b.type;
  }
  
  /// Assert that ParseResult has expected structure
  static bool parseResultValid(ParseResult result) {
    return ParseResultMatchers.isValidParseResult(result);
  }
}

/// Mock data generators for various test scenarios
class MockDataGenerators {
  
  /// Generate random transaction amounts
  static List<double> getRandomAmounts() {
    return [0.01, 1.99, 10.50, 25.00, 100.00, 999.99, 1234.56, 9999.99];
  }
  
  /// Generate various currency codes for testing
  static List<String> getTestCurrencyCodes() {
    return ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR', 'KRW', 'AUD', 'CAD', 'CHF'];
  }
  
  /// Generate test descriptions with keywords
  static Map<String, List<String>> getDescriptionsByCategory() {
    return {
      'food': [
        'lunch at restaurant',
        'coffee from starbucks',
        'groceries at supermarket',
        'dinner with friends',
        'pizza delivery',
      ],
      'transport': [
        'uber ride',
        'bus ticket',
        'gas for car',
        'parking fee',
        'taxi to airport',
      ],
      'entertainment': [
        'movie tickets',
        'concert tickets',
        'video game purchase',
        'streaming subscription',
        'book purchase',
      ],
      'utilities': [
        'electricity bill',
        'water bill',
        'internet payment',
        'phone bill',
        'gas bill',
      ],
    };
  }
}
