import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/mlkit_parser_service.dart';
import '../../lib/services/parser/entity_extractor_base.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/transaction_model.dart';
import '../mocks/mock_entity_extractor.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_localization_service.dart';

/// Integration test that validates complete PRD v1.1.4 "Trust but Verify" scenarios
/// This test validates the entire flow from user input to UI response
/// including timing tests for performance validation
void main() {
  group('Integration PRD v1.1.4 Scenarios', () {
    late MlKitParserService parserService;
    late MockEntityExtractor mockEntityExtractor;
    late MockStorageService mockStorageService;
    late MockLocalizationService mockLocalizationService;

    setUp(() async {
      print('DEBUG: Setting up integration PRD scenarios test');
      
      mockEntityExtractor = MockEntityExtractor();
      mockStorageService = MockStorageService();
      await mockStorageService.init();
      
      mockLocalizationService = MockLocalizationService();
      mockLocalizationService.setupDefaultEnglishData();
      
      // Create parser service instance for testing
      parserService = await MlKitParserService.getInstance(
        mockStorageService,
        entityExtractor: mockEntityExtractor,
      );
      
      print('DEBUG: Integration test setup completed');
    });

    tearDown(() {
      mockEntityExtractor.reset();
      print('DEBUG: Integration test teardown completed');
    });

    group('PRD v1.1.4 Core Scenarios', () {
      test('Scenario: "lux69 100" should trigger amount confirmation with [69, 100]', () async {
        print('\n=== INTEGRATION TEST: lux69 100 scenario ===');
        
        final testInput = 'lux69 100';
        print('DEBUG: Testing input: "$testInput"');
        
        // Configure ML Kit to find "69" embedded in "lux69"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '69',
            start: 3, // Position of "69" in "lux69"
            end: 5,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return [69] at position 3-5');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testInput);
        stopwatch.stop();
        
        final elapsedMs = stopwatch.elapsedMilliseconds;
        print('DEBUG: Parsing completed in ${elapsedMs}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Validate the complete flow
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation for multiple candidates');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateAmounts!.length, 2,
            reason: 'Should have exactly 2 candidates');
        
        // Validate both ML Kit (69) and raw finder (100) candidates are present
        expect(result.candidateAmounts!.contains(69.0), true,
            reason: 'Should include ML Kit candidate 69');
        expect(result.candidateAmounts!.contains(100.0), true,
            reason: 'Should include raw finder candidate 100');
        
        // Validate candidate texts match amounts
        expect(result.candidateTexts!.contains('69'), true);
        expect(result.candidateTexts!.contains('100'), true);
        
        // Validate transaction structure
        expect(result.transaction.description, testInput.trim());
        expect(result.transaction.currencyCode, isNotEmpty);
        expect(result.transaction.type, isNotNull);
        
        // Performance validation
        expect(elapsedMs, lessThan(100),
            reason: 'Parsing should complete within reasonable time');
        
        print('DEBUG: ✅ lux69 100 scenario passed all validations');
      });

      test('Scenario: "restaurant45 mall 200" should detect embedded vs standalone', () async {
        print('\n=== INTEGRATION TEST: restaurant45 mall 200 scenario ===');
        
        final testInput = 'restaurant45 mall 200';
        print('DEBUG: Testing input: "$testInput"');
        
        // Configure ML Kit to find "45" embedded in "restaurant45"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '45',
            start: 10, // Position of "45" in "restaurant45"
            end: 12,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return [45] at position 10-12');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testInput);
        stopwatch.stop();
        
        final elapsedMs = stopwatch.elapsedMilliseconds;
        print('DEBUG: Parsing completed in ${elapsedMs}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Validate the complete flow
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation for embedded vs standalone');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateAmounts!.length, 2,
            reason: 'Should have exactly 2 candidates');
        
        // Should include both embedded (45) and standalone (200) numbers
        expect(result.candidateAmounts!.contains(45.0), true,
            reason: 'Should include embedded number 45');
        expect(result.candidateAmounts!.contains(200.0), true,
            reason: 'Should include standalone number 200');
        
        // Validate candidate texts
        expect(result.candidateTexts!.contains('45'), true);
        expect(result.candidateTexts!.contains('200'), true);
        
        // Performance validation
        expect(elapsedMs, lessThan(100),
            reason: 'Parsing should complete within reasonable time');
        
        print('DEBUG: ✅ restaurant45 mall 200 scenario passed all validations');
      });

      test('Scenario: "cafe88 bill 150" should consolidate ML Kit and raw finder', () async {
        print('\n=== INTEGRATION TEST: cafe88 bill 150 scenario ===');
        
        final testInput = 'cafe88 bill 150';
        print('DEBUG: Testing input: "$testInput"');
        
        // Configure ML Kit to find "88" embedded in "cafe88"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '88',
            start: 4, // Position of "88" in "cafe88"
            end: 6,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return [88] at position 4-6');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testInput);
        stopwatch.stop();
        
        final elapsedMs = stopwatch.elapsedMilliseconds;
        print('DEBUG: Parsing completed in ${elapsedMs}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Validate consolidation worked correctly
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation for consolidated candidates');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateAmounts!.length, 2,
            reason: 'Should have exactly 2 consolidated candidates');
        
        // Should consolidate ML Kit (88) and raw finder (150) results
        expect(result.candidateAmounts!.contains(88.0), true,
            reason: 'Should include ML Kit candidate 88');
        expect(result.candidateAmounts!.contains(150.0), true,
            reason: 'Should include raw finder candidate 150');
        
        // Validate candidate texts
        expect(result.candidateTexts!.contains('88'), true);
        expect(result.candidateTexts!.contains('150'), true);
        
        // Performance validation
        expect(elapsedMs, lessThan(100),
            reason: 'Parsing should complete within reasonable time');
        
        print('DEBUG: ✅ cafe88 bill 150 scenario passed all validations');
      });
    });

    group('Edge Case Integration Tests', () {
      test('ML Kit empty, raw finder finds multiple numbers', () async {
        print('\n=== INTEGRATION TEST: ML Kit empty, raw finder multiple ===');
        
        final testInput = 'spent 50 and 75 today';
        print('DEBUG: Testing input: "$testInput"');
        
        // Configure ML Kit to return empty results
        mockEntityExtractor.setMockResults([]);
        print('DEBUG: ML Kit configured to return empty results');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testInput);
        stopwatch.stop();
        
        final elapsedMs = stopwatch.elapsedMilliseconds;
        print('DEBUG: Parsing completed in ${elapsedMs}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Should still trigger amount confirmation from raw finder results
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation from raw finder alone');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateAmounts!.length, 2,
            reason: 'Should have 2 candidates from raw finder');
        
        expect(result.candidateAmounts!.contains(50.0), true);
        expect(result.candidateAmounts!.contains(75.0), true);
        
        // Performance validation
        expect(elapsedMs, lessThan(100),
            reason: 'Parsing should complete within reasonable time');
        
        print('DEBUG: ✅ ML Kit empty scenario passed all validations');
      });

      test('Both find same number (deduplication)', () async {
        print('\n=== INTEGRATION TEST: Deduplication scenario ===');
        
        final testInput = 'paid 100 dollars';
        print('DEBUG: Testing input: "$testInput"');
        
        // Configure ML Kit to find "100" at the same position raw finder would find it
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '100',
            start: 5, // Position of "100" in "paid 100 dollars"
            end: 8,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return [100] at position 5-8');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testInput);
        stopwatch.stop();
        
        final elapsedMs = stopwatch.elapsedMilliseconds;
        print('DEBUG: Parsing completed in ${elapsedMs}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Should not trigger amount confirmation for single deduplicated candidate
        expect(result.status, isNot(ParseStatus.needsAmountConfirmation),
            reason: 'Should not trigger confirmation for single deduplicated candidate');
        
        // Should have successful parsing with single amount
        expect(result.status, anyOf([ParseStatus.success, ParseStatus.needsType]),
            reason: 'Should have successful parsing or need type selection');
        expect(result.transaction.amount, 100.0,
            reason: 'Should use the deduplicated amount');
        
        // Performance validation
        expect(elapsedMs, lessThan(100),
            reason: 'Parsing should complete within reasonable time');
        
        print('DEBUG: ✅ Deduplication scenario passed all validations');
      });
    });

    group('Performance Integration Tests', () {
      test('Complex multiple number scenarios complete within time limits', () async {
        print('\n=== INTEGRATION TEST: Performance validation ===');
        
        final complexScenarios = [
          'simple 100',
          'complex shop123 mall456 total 789',
          'very complex restaurant99 bill 250 tip 50 tax 25',
          'extreme case store1 item2 subtotal3 discount4 final5',
        ];
        
        for (final testInput in complexScenarios) {
          print('DEBUG: Testing performance for: "$testInput"');
          
          // Configure ML Kit for complex scenario (find first embedded number)
          final firstNumberMatch = RegExp(r'\d+').firstMatch(testInput);
          if (firstNumberMatch != null) {
            mockEntityExtractor.setMockResults([
              MockEntityAnnotation(
                text: firstNumberMatch.group(0)!,
                start: firstNumberMatch.start,
                end: firstNumberMatch.end,
                entityType: EntityType.money,
              ),
            ]);
          }
          
          final stopwatch = Stopwatch()..start();
          final result = await parserService.parseTransaction(testInput);
          stopwatch.stop();
          
          final elapsedMs = stopwatch.elapsedMilliseconds;
          print('DEBUG: Parsing completed in ${elapsedMs}ms');
          print('DEBUG: Result status: ${result.status}');
          
          // Performance should be reasonable (under 200ms for complex cases)
          expect(elapsedMs, lessThan(200),
              reason: 'Complex parsing should complete within reasonable time');
          
          // Should produce valid result
          expect(result.status, isNotNull);
          expect(result.transaction, isNotNull);
          
          print('DEBUG: Performance test passed for: "$testInput"');
        }
        
        print('DEBUG: ✅ All performance tests passed');
      });
    });

    group('End-to-End Flow Validation', () {
      test('Complete flow from input to amount confirmation response', () async {
        print('\n=== INTEGRATION TEST: Complete end-to-end flow ===');
        
        final testInput = 'lux69 100';
        print('DEBUG: Testing complete flow for: "$testInput"');
        
        // Step 1: Initial parsing
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '69',
            start: 3,
            end: 5,
            entityType: EntityType.money,
          ),
        ]);
        
        final initialResult = await parserService.parseTransaction(testInput);
        print('DEBUG: Step 1 - Initial parsing result: ${initialResult.status}');
        
        expect(initialResult.status, ParseStatus.needsAmountConfirmation);
        expect(initialResult.candidateAmounts, isNotNull);
        expect(initialResult.candidateTexts, isNotNull);
        
        // Step 2: Simulate user selection (selecting "100")
        final selectedAmount = 100.0;
        final selectedText = '100';
        print('DEBUG: Step 2 - User selects: $selectedText ($selectedAmount)');
        
        // Step 3: Complete transaction with selected amount
        final completedResult = await parserService.completeTransaction(
          testInput,
          selectedAmount,
        );
        print('DEBUG: Step 3 - Transaction completion result: ${completedResult.status}');

        // Validate final result
        expect(completedResult.status, anyOf([ParseStatus.success, ParseStatus.needsType]),
            reason: 'Should complete successfully or need type selection');
        expect(completedResult.transaction.amount, selectedAmount,
            reason: 'Should use the selected amount');
        expect(completedResult.transaction.description, testInput.trim(),
            reason: 'Should preserve original description');
        
        print('DEBUG: ✅ Complete end-to-end flow validation passed');
      });
    });
  });
}
