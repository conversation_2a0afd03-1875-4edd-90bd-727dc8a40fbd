import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/mlkit_parser_service.dart';
import '../../lib/services/parser/entity_extractor_base.dart';
import '../../lib/models/parse_result.dart';
import '../mocks/mock_entity_extractor.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_localization_service.dart';

/// ADB Log Monitor Test
/// This test validates that proper log output is generated during testing
/// with distinctive log markers for ADB monitoring and includes instructions
/// for interpreting the output
void main() {
  group('ADB Log Monitor Tests', () {
    late MlKitParserService parserService;
    late MockEntityExtractor mockEntityExtractor;
    late MockStorageService mockStorageService;
    late MockLocalizationService mockLocalizationService;

    setUp(() async {
      print('DEBUG: Setting up ADB log monitor test');
      
      mockEntityExtractor = MockEntityExtractor();
      mockStorageService = MockStorageService();
      await mockStorageService.init();
      
      mockLocalizationService = MockLocalizationService();
      mockLocalizationService.setupDefaultEnglishData();
      
      // Create parser service instance for testing
      parserService = await MlKitParserService.getInstance(
        mockStorageService,
        entityExtractor: mockEntityExtractor,
      );
      
      print('DEBUG: ADB log monitor test setup completed');
    });

    tearDown(() {
      mockEntityExtractor.reset();
      print('DEBUG: ADB log monitor test teardown completed');
    });

    group('Distinctive Log Markers', () {
      test('generates distinctive log markers for ADB monitoring', () async {
        print('\n=== ADB LOG MONITOR TEST: Distinctive markers ===');
        print('🔍 ADB_MONITOR_START: Beginning distinctive log marker test');
        
        final testInput = 'lux69 100';
        print('🔍 ADB_MONITOR_INPUT: Testing input "$testInput"');
        
        // Configure ML Kit to find "69" embedded in "lux69"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '69',
            start: 3,
            end: 5,
            entityType: EntityType.money,
          ),
        ]);
        
        print('🔍 ADB_MONITOR_MLKIT: ML Kit configured to return [69] at position 3-5');
        
        final result = await parserService.parseTransaction(testInput);
        
        print('🔍 ADB_MONITOR_RESULT: Parse status = ${result.status}');
        print('🔍 ADB_MONITOR_CANDIDATES: Candidate amounts = ${result.candidateAmounts}');
        print('🔍 ADB_MONITOR_TEXTS: Candidate texts = ${result.candidateTexts}');
        
        // Validate the result
        expect(result.status, ParseStatus.needsAmountConfirmation);
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        expect(result.candidateAmounts!.length, 2);
        
        print('🔍 ADB_MONITOR_VALIDATION: All validations passed');
        print('🔍 ADB_MONITOR_END: Distinctive log marker test completed');
      });

      test('generates step-by-step parsing flow markers', () async {
        print('\n=== ADB LOG MONITOR TEST: Step-by-step flow markers ===');
        print('🔍 ADB_FLOW_START: Beginning step-by-step parsing flow');
        
        final testInput = 'restaurant45 mall 200';
        print('🔍 ADB_FLOW_STEP1: Input received = "$testInput"');
        
        // Configure ML Kit
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '45',
            start: 10,
            end: 12,
            entityType: EntityType.money,
          ),
        ]);
        
        print('🔍 ADB_FLOW_STEP2: ML Kit mock configured');
        print('🔍 ADB_FLOW_STEP3: Starting transaction parsing');
        
        final result = await parserService.parseTransaction(testInput);
        
        print('🔍 ADB_FLOW_STEP4: Parsing completed');
        print('🔍 ADB_FLOW_STEP5: Result analysis starting');
        
        if (result.status == ParseStatus.needsAmountConfirmation) {
          print('🔍 ADB_FLOW_CONFIRMATION: Amount confirmation triggered');
          print('🔍 ADB_FLOW_OPTIONS: Available options = ${result.candidateTexts}');
          
          // Simulate user selection
          final selectedAmount = result.candidateAmounts![1]; // Select second option
          print('🔍 ADB_FLOW_SELECTION: User selected amount = $selectedAmount');
          
          final completedResult = await parserService.completeTransaction(
            testInput,
            selectedAmount,
          );
          
          print('🔍 ADB_FLOW_COMPLETION: Transaction completed with status = ${completedResult.status}');
        }
        
        print('🔍 ADB_FLOW_END: Step-by-step flow completed');
      });

      test('generates performance timing markers', () async {
        print('\n=== ADB LOG MONITOR TEST: Performance timing markers ===');
        print('🔍 ADB_PERF_START: Beginning performance timing test');
        
        final testInputs = [
          'simple 100',
          'complex shop123 mall456 total 789',
          'very complex restaurant99 bill 250 tip 50 tax 25',
        ];
        
        for (int i = 0; i < testInputs.length; i++) {
          final testInput = testInputs[i];
          print('🔍 ADB_PERF_TEST${i + 1}_START: Testing "$testInput"');
          
          // Configure ML Kit with first number
          final firstNumberMatch = RegExp(r'\d+').firstMatch(testInput);
          if (firstNumberMatch != null) {
            mockEntityExtractor.setMockResults([
              MockEntityAnnotation(
                text: firstNumberMatch.group(0)!,
                start: firstNumberMatch.start,
                end: firstNumberMatch.end,
                entityType: EntityType.money,
              ),
            ]);
          }
          
          final stopwatch = Stopwatch()..start();
          print('🔍 ADB_PERF_TIMER_START: Parsing timer started');
          
          final result = await parserService.parseTransaction(testInput);
          
          stopwatch.stop();
          final elapsedMs = stopwatch.elapsedMilliseconds;
          
          print('🔍 ADB_PERF_TIMER_END: Parsing completed in ${elapsedMs}ms');
          print('🔍 ADB_PERF_RESULT: Status = ${result.status}');
          print('🔍 ADB_PERF_TEST${i + 1}_END: Performance test completed');
          
          // Validate performance
          expect(elapsedMs, lessThan(200), reason: 'Parsing should be reasonably fast');
        }
        
        print('🔍 ADB_PERF_END: All performance timing tests completed');
      });
    });

    group('Error Scenario Markers', () {
      test('generates error handling markers', () async {
        print('\n=== ADB LOG MONITOR TEST: Error handling markers ===');
        print('🔍 ADB_ERROR_START: Beginning error handling test');
        
        // Test empty input
        print('🔍 ADB_ERROR_CASE1: Testing empty input');
        mockEntityExtractor.setMockResults([]);
        
        final emptyResult = await parserService.parseTransaction('');
        print('🔍 ADB_ERROR_RESULT1: Empty input result = ${emptyResult.status}');
        
        // Test no numbers input
        print('🔍 ADB_ERROR_CASE2: Testing no numbers input');
        final noNumbersResult = await parserService.parseTransaction('hello world');
        print('🔍 ADB_ERROR_RESULT2: No numbers result = ${noNumbersResult.status}');
        
        // Test ML Kit error simulation (skip if method not available)
        print('🔍 ADB_ERROR_CASE3: Testing ML Kit error simulation');

        try {
          await parserService.parseTransaction('test 100');
          print('🔍 ADB_ERROR_RESULT3: ML Kit error handled gracefully');
        } catch (e) {
          print('🔍 ADB_ERROR_CAUGHT: Exception caught = $e');
        }
        
        print('🔍 ADB_ERROR_END: Error handling test completed');
      });
    });

    group('Integration Flow Markers', () {
      test('generates complete integration flow markers', () async {
        print('\n=== ADB LOG MONITOR TEST: Complete integration flow ===');
        print('🔍 ADB_INTEGRATION_START: Beginning complete integration flow');
        
        final testInput = 'cafe88 bill 150';
        print('🔍 ADB_INTEGRATION_INPUT: Processing "$testInput"');
        
        // Step 1: Initial parsing
        print('🔍 ADB_INTEGRATION_PHASE1: Initial parsing phase');
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '88',
            start: 4,
            end: 6,
            entityType: EntityType.money,
          ),
        ]);
        
        final initialResult = await parserService.parseTransaction(testInput);
        print('🔍 ADB_INTEGRATION_PHASE1_RESULT: ${initialResult.status}');
        
        if (initialResult.status == ParseStatus.needsAmountConfirmation) {
          print('🔍 ADB_INTEGRATION_PHASE2: Amount confirmation phase');
          print('🔍 ADB_INTEGRATION_CANDIDATES: ${initialResult.candidateTexts}');
          
          // Step 2: User selection simulation
          final selectedAmount = initialResult.candidateAmounts![1]; // Select "150"
          print('🔍 ADB_INTEGRATION_SELECTION: User selected $selectedAmount');
          
          // Step 3: Transaction completion
          print('🔍 ADB_INTEGRATION_PHASE3: Transaction completion phase');
          final completedResult = await parserService.completeTransaction(
            testInput,
            selectedAmount,
          );

          print('🔍 ADB_INTEGRATION_PHASE3_RESULT: ${completedResult.status}');
          print('🔍 ADB_INTEGRATION_FINAL_AMOUNT: ${completedResult.transaction.amount}');

          // Validate final result
          expect(completedResult.transaction.amount, selectedAmount);
          expect(completedResult.status, anyOf([ParseStatus.success, ParseStatus.needsType]));
        }
        
        print('🔍 ADB_INTEGRATION_END: Complete integration flow finished');
      });
    });

    group('Log Pattern Validation', () {
      test('validates expected log patterns are generated', () async {
        print('\n=== ADB LOG MONITOR TEST: Log pattern validation ===');
        print('🔍 ADB_PATTERN_START: Beginning log pattern validation');
        
        final testInput = 'lux69 100';
        print('🔍 ADB_PATTERN_INPUT: "$testInput"');
        
        // This test ensures that the expected log patterns are generated
        // These patterns should be visible in ADB logs when running on device
        
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '69',
            start: 3,
            end: 5,
            entityType: EntityType.money,
          ),
        ]);
        
        print('🔍 ADB_PATTERN_EXPECTED: ML Kit entities found');
        print('🔍 ADB_PATTERN_EXPECTED: Raw number finder results');
        print('🔍 ADB_PATTERN_EXPECTED: Consolidated candidates');
        print('🔍 ADB_PATTERN_EXPECTED: Amount confirmation triggered');
        
        final result = await parserService.parseTransaction(testInput);
        
        // These log messages should appear in the actual parser service
        // when running with debug logging enabled
        print('🔍 ADB_PATTERN_VALIDATION: Checking for expected patterns');
        
        expect(result.status, ParseStatus.needsAmountConfirmation);
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        
        print('🔍 ADB_PATTERN_SUCCESS: All expected patterns should be visible in ADB logs');
        print('🔍 ADB_PATTERN_END: Log pattern validation completed');
      });
    });
  });
}

/// Instructions for interpreting ADB log output
/// 
/// To monitor logs during testing:
/// 1. Run: ./scripts/debug_multiple_numbers.sh
/// 2. Look for these distinctive markers:
///    - 🔍 ADB_MONITOR_*: General monitoring markers
///    - 🔍 ADB_FLOW_*: Step-by-step flow markers  
///    - 🔍 ADB_PERF_*: Performance timing markers
///    - 🔍 ADB_ERROR_*: Error handling markers
///    - 🔍 ADB_INTEGRATION_*: Integration flow markers
///    - 🔍 ADB_PATTERN_*: Log pattern validation markers
///
/// Expected log patterns from MlKitParserService:
/// - "ML Kit entities found: [...]"
/// - "Raw number finder results: [...]"  
/// - "Consolidated candidates: [...]"
/// - "Amount confirmation triggered with candidates: [...]"
/// - "User selected amount: X"
/// - "Transaction completed with amount: X"
///
/// Expected log patterns from ChatScreen:
/// - "Amount confirmation triggered for candidates: [...]"
/// - "Creating system message with quick replies: [...]"
/// - "User selected amount option: X"
/// - "Amount confirmation response processed: X"
///
/// Color coding in debug script:
/// - 🔵 BLUE: ML Kit related logs
/// - 🟢 GREEN: Raw finder related logs  
/// - 🟡 YELLOW: Consolidation related logs
/// - 🔴 RED: Amount confirmation related logs
/// - 🟣 PURPLE: User interaction related logs
///
/// Performance expectations:
/// - Simple parsing: < 50ms
/// - Complex parsing: < 100ms  
/// - Very complex parsing: < 200ms
///
/// To run this test specifically:
/// flutter test test/debug/adb_log_monitor_test.dart --verbose
