import 'dart:io';
import 'lib/services/parser/mlkit_parser_service.dart';
import 'lib/services/storage_service.dart';
import 'lib/models/parse_result.dart';
import 'lib/models/transaction_model.dart';
import 'test/mocks/mock_entity_extractor.dart';

/// Interactive manual testing script for multiple number detection
/// This script provides a menu-driven interface for testing various scenarios
/// with detailed output and support for custom input and mock ML Kit responses
void main() async {
  print('=== Multiple Number Detection Manual Testing Tool ===');
  print('This tool allows interactive testing of the PRD v1.1.4 "Trust but Verify" approach');
  print('');

  // Initialize services
  print('Initializing services...');
  final storageService = StorageService();
  await storageService.init();
  
  final mockEntityExtractor = MockEntityExtractor();
  final parserService = await MlKitParserService.getInstance(
    storageService,
    entityExtractor: mockEntityExtractor,
  );
  
  print('✅ Services initialized successfully');
  print('');

  while (true) {
    showMainMenu();
    final choice = await getUserInput('Enter your choice: ');
    
    switch (choice) {
      case '1':
        await testPRDScenarios(parserService, mockEntityExtractor);
        break;
      case '2':
        await testCustomInput(parserService, mockEntityExtractor);
        break;
      case '3':
        await testEdgeCases(parserService, mockEntityExtractor);
        break;
      case '4':
        await testPerformance(parserService, mockEntityExtractor);
        break;
      case '5':
        await testAmountConfirmationFlow(parserService, mockEntityExtractor);
        break;
      case '6':
        showDebugInstructions();
        break;
      case '0':
        print('Goodbye!');
        exit(0);
      default:
        print('❌ Invalid choice. Please try again.');
        print('');
    }
  }
}

void showMainMenu() {
  print('=== MAIN MENU ===');
  print('1. Test PRD v1.1.4 Core Scenarios');
  print('2. Test Custom Input');
  print('3. Test Edge Cases');
  print('4. Test Performance');
  print('5. Test Amount Confirmation Flow');
  print('6. Show Debug Instructions');
  print('0. Exit');
  print('');
}

Future<String> getUserInput(String prompt) async {
  stdout.write(prompt);
  return stdin.readLineSync() ?? '';
}

Future<void> testPRDScenarios(MlKitParserService parserService, MockEntityExtractor mockExtractor) async {
  print('\n=== PRD v1.1.4 Core Scenarios ===');
  
  final scenarios = [
    {
      'name': 'lux69 100 (embedded vs standalone)',
      'input': 'lux69 100',
      'mlkitEntity': {'text': '69', 'start': 3, 'end': 5},
      'expectedCandidates': [69.0, 100.0],
    },
    {
      'name': 'restaurant45 mall 200 (embedded vs standalone)',
      'input': 'restaurant45 mall 200',
      'mlkitEntity': {'text': '45', 'start': 10, 'end': 12},
      'expectedCandidates': [45.0, 200.0],
    },
    {
      'name': 'cafe88 bill 150 (consolidation)',
      'input': 'cafe88 bill 150',
      'mlkitEntity': {'text': '88', 'start': 4, 'end': 6},
      'expectedCandidates': [88.0, 150.0],
    },
  ];

  for (int i = 0; i < scenarios.length; i++) {
    final scenario = scenarios[i];
    print('\n--- Scenario ${i + 1}: ${scenario['name']} ---');
    print('Input: "${scenario['input']}"');
    
    // Configure ML Kit mock
    final mlkitEntity = scenario['mlkitEntity'] as Map<String, dynamic>;
    mockExtractor.setMockResults([
      MockEntityAnnotation(
        text: mlkitEntity['text'] as String,
        start: mlkitEntity['start'] as int,
        end: mlkitEntity['end'] as int,
        entityType: EntityType.money,
      ),
    ]);
    
    print('ML Kit configured to return: [${mlkitEntity['text']}] at position ${mlkitEntity['start']}-${mlkitEntity['end']}');
    
    // Execute parsing
    final stopwatch = Stopwatch()..start();
    final result = await parserService.parseTransaction(scenario['input'] as String);
    stopwatch.stop();
    
    // Display results
    print('⏱️  Parsing time: ${stopwatch.elapsedMilliseconds}ms');
    print('📊 Result status: ${result.status}');
    print('💰 Candidate amounts: ${result.candidateAmounts}');
    print('📝 Candidate texts: ${result.candidateTexts}');
    
    // Validate expectations
    final expectedCandidates = scenario['expectedCandidates'] as List<double>;
    if (result.status == ParseStatus.needsAmountConfirmation) {
      final hasAllExpected = expectedCandidates.every(
        (expected) => result.candidateAmounts?.contains(expected) ?? false
      );
      print(hasAllExpected ? '✅ All expected candidates found' : '❌ Missing expected candidates');
    } else {
      print('⚠️  Did not trigger amount confirmation as expected');
    }
    
    print('Press Enter to continue...');
    await getUserInput('');
  }
}

Future<void> testCustomInput(MlKitParserService parserService, MockEntityExtractor mockExtractor) async {
  print('\n=== Custom Input Testing ===');
  
  while (true) {
    final input = await getUserInput('Enter text to parse (or "back" to return): ');
    if (input.toLowerCase() == 'back') break;
    
    if (input.trim().isEmpty) {
      print('❌ Please enter some text to parse.');
      continue;
    }
    
    print('\nConfiguring ML Kit mock...');
    print('Options:');
    print('1. No ML Kit entities (empty)');
    print('2. Configure custom ML Kit entity');
    print('3. Use automatic detection');
    
    final mockChoice = await getUserInput('Choose ML Kit configuration: ');
    
    switch (mockChoice) {
      case '1':
        mockExtractor.setMockResults([]);
        print('✅ ML Kit configured to return empty results');
        break;
      case '2':
        await configureCustomMLKitEntity(mockExtractor, input);
        break;
      case '3':
        await configureAutomaticMLKitEntity(mockExtractor, input);
        break;
      default:
        print('Using empty ML Kit results as default');
        mockExtractor.setMockResults([]);
    }
    
    // Execute parsing
    print('\n🔄 Parsing: "$input"');
    final stopwatch = Stopwatch()..start();
    final result = await parserService.parseTransaction(input);
    stopwatch.stop();
    
    // Display detailed results
    displayDetailedResults(result, stopwatch.elapsedMilliseconds);
    
    if (result.status == ParseStatus.needsAmountConfirmation) {
      await simulateAmountConfirmation(parserService, result);
    }
    
    print('\n' + '='*50);
  }
}

Future<void> configureCustomMLKitEntity(MockEntityExtractor mockExtractor, String input) async {
  final entityText = await getUserInput('Enter entity text to find: ');
  final startPos = input.indexOf(entityText);
  
  if (startPos == -1) {
    print('❌ Entity text not found in input. Using position 0.');
    mockExtractor.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: 0,
        end: entityText.length,
        entityType: EntityType.money,
      ),
    ]);
  } else {
    mockExtractor.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: startPos,
        end: startPos + entityText.length,
        entityType: EntityType.money,
      ),
    ]);
    print('✅ ML Kit configured to return [$entityText] at position $startPos-${startPos + entityText.length}');
  }
}

Future<void> configureAutomaticMLKitEntity(MockEntityExtractor mockExtractor, String input) async {
  // Find first number in the input
  final numberMatch = RegExp(r'\d+').firstMatch(input);
  if (numberMatch != null) {
    mockExtractor.setMockResults([
      MockEntityAnnotation(
        text: numberMatch.group(0)!,
        start: numberMatch.start,
        end: numberMatch.end,
        entityType: EntityType.money,
      ),
    ]);
    print('✅ ML Kit configured to return [${numberMatch.group(0)}] at position ${numberMatch.start}-${numberMatch.end}');
  } else {
    mockExtractor.setMockResults([]);
    print('✅ No numbers found, ML Kit configured to return empty results');
  }
}

void displayDetailedResults(ParseResult result, int elapsedMs) {
  print('\n📊 PARSING RESULTS:');
  print('⏱️  Time: ${elapsedMs}ms');
  print('🎯 Status: ${result.status}');
  print('💰 Amount: ${result.transaction.amount}');
  print('📝 Description: "${result.transaction.description}"');
  print('🏷️  Type: ${result.transaction.type}');
  print('📂 Category: ${result.transaction.categoryId}');
  print('💱 Currency: ${result.transaction.currencyCode}');
  
  if (result.candidateAmounts != null && result.candidateAmounts!.isNotEmpty) {
    print('🔢 Candidate amounts: ${result.candidateAmounts}');
    print('📄 Candidate texts: ${result.candidateTexts}');
  }
}

Future<void> simulateAmountConfirmation(MlKitParserService parserService, ParseResult result) async {
  print('\n💬 AMOUNT CONFIRMATION SIMULATION:');
  print('Multiple amounts detected. Please choose:');
  
  for (int i = 0; i < result.candidateTexts!.length; i++) {
    print('${i + 1}. ${result.candidateTexts![i]} (${result.candidateAmounts![i]})');
  }
  print('${result.candidateTexts!.length + 1}. Cancel');
  
  final choice = await getUserInput('Enter your choice: ');
  final choiceIndex = int.tryParse(choice);
  
  if (choiceIndex != null && choiceIndex > 0 && choiceIndex <= result.candidateTexts!.length) {
    final selectedAmount = result.candidateAmounts![choiceIndex - 1];
    print('✅ Selected amount: $selectedAmount');
    
    // Complete the transaction
    final completedResult = await parserService.completeTransaction(
      result.transaction,
      selectedAmount,
    );
    
    print('🎯 Final status: ${completedResult.status}');
    print('💰 Final amount: ${completedResult.transaction.amount}');
  } else if (choiceIndex == result.candidateTexts!.length + 1) {
    print('❌ User cancelled amount selection');
  } else {
    print('❌ Invalid choice');
  }
}

Future<void> testEdgeCases(MlKitParserService parserService, MockEntityExtractor mockExtractor) async {
  print('\n=== Edge Cases Testing ===');
  
  final edgeCases = [
    {
      'name': 'Empty input',
      'input': '',
      'mlkitConfig': 'empty',
    },
    {
      'name': 'No numbers',
      'input': 'hello world',
      'mlkitConfig': 'empty',
    },
    {
      'name': 'Single number',
      'input': 'paid 100',
      'mlkitConfig': 'single',
    },
    {
      'name': 'Many numbers',
      'input': 'shop1 item2 subtotal3 tax4 total5',
      'mlkitConfig': 'first',
    },
    {
      'name': 'Decimal numbers',
      'input': 'paid 12.50 and 25.75',
      'mlkitConfig': 'first',
    },
  ];

  for (final edgeCase in edgeCases) {
    print('\n--- Testing: ${edgeCase['name']} ---');
    print('Input: "${edgeCase['input']}"');
    
    // Configure ML Kit based on strategy
    configureMLKitForEdgeCase(mockExtractor, edgeCase['input'] as String, edgeCase['mlkitConfig'] as String);
    
    final stopwatch = Stopwatch()..start();
    final result = await parserService.parseTransaction(edgeCase['input'] as String);
    stopwatch.stop();
    
    print('⏱️  Time: ${stopwatch.elapsedMilliseconds}ms');
    print('🎯 Status: ${result.status}');
    print('💰 Amount: ${result.transaction.amount}');
    print('🔢 Candidates: ${result.candidateAmounts}');
    
    print('Press Enter to continue...');
    await getUserInput('');
  }
}

void configureMLKitForEdgeCase(MockEntityExtractor mockExtractor, String input, String strategy) {
  switch (strategy) {
    case 'empty':
      mockExtractor.setMockResults([]);
      print('ML Kit: Empty results');
      break;
    case 'single':
      final match = RegExp(r'\d+(?:\.\d+)?').firstMatch(input);
      if (match != null) {
        mockExtractor.setMockResults([
          MockEntityAnnotation(
            text: match.group(0)!,
            start: match.start,
            end: match.end,
            entityType: EntityType.money,
          ),
        ]);
        print('ML Kit: Single entity [${match.group(0)}]');
      } else {
        mockExtractor.setMockResults([]);
        print('ML Kit: No numbers found, empty results');
      }
      break;
    case 'first':
      final match = RegExp(r'\d+').firstMatch(input);
      if (match != null) {
        mockExtractor.setMockResults([
          MockEntityAnnotation(
            text: match.group(0)!,
            start: match.start,
            end: match.end,
            entityType: EntityType.money,
          ),
        ]);
        print('ML Kit: First number [${match.group(0)}]');
      } else {
        mockExtractor.setMockResults([]);
        print('ML Kit: No numbers found, empty results');
      }
      break;
  }
}

Future<void> testPerformance(MlKitParserService parserService, MockEntityExtractor mockExtractor) async {
  print('\n=== Performance Testing ===');
  
  final performanceTests = [
    'simple 100',
    'complex shop123 mall456 total 789',
    'very complex restaurant99 bill 250 tip 50 tax 25',
    'extreme case store1 item2 subtotal3 discount4 final5 extra6 bonus7 fee8 charge9 amount10',
  ];

  print('Running performance tests...\n');
  
  for (int i = 0; i < performanceTests.length; i++) {
    final testInput = performanceTests[i];
    print('Test ${i + 1}: "$testInput"');
    
    // Configure ML Kit with first number
    final match = RegExp(r'\d+').firstMatch(testInput);
    if (match != null) {
      mockExtractor.setMockResults([
        MockEntityAnnotation(
          text: match.group(0)!,
          start: match.start,
          end: match.end,
          entityType: EntityType.money,
        ),
      ]);
    }
    
    // Run multiple iterations for average
    final times = <int>[];
    for (int j = 0; j < 5; j++) {
      final stopwatch = Stopwatch()..start();
      await parserService.parseTransaction(testInput);
      stopwatch.stop();
      times.add(stopwatch.elapsedMilliseconds);
    }
    
    final avgTime = times.reduce((a, b) => a + b) / times.length;
    final minTime = times.reduce((a, b) => a < b ? a : b);
    final maxTime = times.reduce((a, b) => a > b ? a : b);
    
    print('  Average: ${avgTime.toStringAsFixed(1)}ms');
    print('  Min: ${minTime}ms, Max: ${maxTime}ms');
    print('  Status: ${avgTime < 100 ? "✅ GOOD" : avgTime < 200 ? "⚠️  ACCEPTABLE" : "❌ SLOW"}');
    print('');
  }
}

Future<void> testAmountConfirmationFlow(MlKitParserService parserService, MockEntityExtractor mockExtractor) async {
  print('\n=== Amount Confirmation Flow Testing ===');
  print('This test simulates the complete user interaction flow.');
  print('');
  
  final testInput = await getUserInput('Enter text with multiple amounts (or press Enter for "lux69 100"): ');
  final actualInput = testInput.trim().isEmpty ? 'lux69 100' : testInput;
  
  print('\n🔄 Step 1: Initial parsing');
  print('Input: "$actualInput"');
  
  // Configure ML Kit to find first embedded number
  final match = RegExp(r'[a-zA-Z]+(\d+)').firstMatch(actualInput);
  if (match != null) {
    final numberText = match.group(1)!;
    final numberStart = match.start + (match.group(0)!.length - numberText.length);
    mockExtractor.setMockResults([
      MockEntityAnnotation(
        text: numberText,
        start: numberStart,
        end: numberStart + numberText.length,
        entityType: EntityType.money,
      ),
    ]);
    print('ML Kit configured to find embedded number: $numberText');
  } else {
    mockExtractor.setMockResults([]);
    print('No embedded numbers found, using empty ML Kit results');
  }
  
  final result = await parserService.parseTransaction(actualInput);
  print('Result: ${result.status}');
  
  if (result.status == ParseStatus.needsAmountConfirmation) {
    print('\n💬 Step 2: Amount confirmation triggered');
    print('System message: "I found multiple possible amounts in your message. Which amount did you mean?"');
    print('Quick reply options:');
    for (int i = 0; i < result.candidateTexts!.length; i++) {
      print('  [${result.candidateTexts![i]}]');
    }
    print('  [Cancel]');
    
    print('\nSimulating user selection...');
    for (int i = 0; i < result.candidateTexts!.length; i++) {
      print('${i + 1}. ${result.candidateTexts![i]} (${result.candidateAmounts![i]})');
    }
    
    final choice = await getUserInput('Select an option (1-${result.candidateTexts!.length}): ');
    final choiceIndex = int.tryParse(choice);
    
    if (choiceIndex != null && choiceIndex > 0 && choiceIndex <= result.candidateTexts!.length) {
      final selectedAmount = result.candidateAmounts![choiceIndex - 1];
      print('\n🎯 Step 3: User selected ${result.candidateTexts![choiceIndex - 1]} ($selectedAmount)');
      
      final completedResult = await parserService.completeTransaction(
        result.transaction,
        selectedAmount,
      );
      
      print('✅ Step 4: Transaction completed');
      print('Final status: ${completedResult.status}');
      print('Final amount: ${completedResult.transaction.amount}');
      print('Description: "${completedResult.transaction.description}"');
    } else {
      print('❌ Invalid selection or cancelled');
    }
  } else {
    print('⚠️  Amount confirmation was not triggered');
    print('This might be expected if there\'s only one candidate or no numbers found');
  }
}

void showDebugInstructions() {
  print('\n=== Debug Instructions ===');
  print('');
  print('🔧 ADB Log Monitoring:');
  print('   Run: ./scripts/debug_multiple_numbers.sh');
  print('   This will show real-time logs with color coding');
  print('');
  print('🧪 Running Tests:');
  print('   flutter test test/debug/multiple_number_debug_test.dart');
  print('   flutter test test/debug/ui_amount_confirmation_test.dart');
  print('   flutter test test/debug/integration_prd_scenarios_test.dart');
  print('');
  print('📱 Testing on Device:');
  print('   1. Connect Android device or start emulator');
  print('   2. Run: flutter run');
  print('   3. In another terminal: ./scripts/debug_multiple_numbers.sh');
  print('   4. Test inputs like "lux69 100" in the chat');
  print('');
  print('🔍 Key Log Patterns to Watch:');
  print('   - "ML Kit entities found"');
  print('   - "Raw number finder results"');
  print('   - "Consolidated candidates"');
  print('   - "Amount confirmation triggered"');
  print('   - "User selected amount"');
  print('');
  print('Press Enter to continue...');
  getUserInput('');
}
