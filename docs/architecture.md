# Architecture Documentation

This document describes the architecture of the DreamFlow application, including its components, data flow, and key design patterns.

**Project**: DreamFlow (Money Lover Chat)
**Architecture Pattern**: Layered Architecture with Provider State Management
**Primary Language**: Dart/Flutter

## Architectural Overview

The application follows a layered architecture pattern with Provider for state management:

```mermaid
graph TD
    UI[UI Layer - Screens & Widgets]
    BL[Business Logic Layer - Providers]
    SL[Service Layer]
    DL[Data Layer - Models & Storage]
    
    UI --> BL
    BL --> SL
    SL --> DL
    BL --> DL
```

## Component Diagram

The major components of the application and their interactions, including the new learning and localization services:

```mermaid
graph TD
    subgraph "Presentation Layer"
        CS[Categories Screen]
        ChS[Chat Screen]
        SS[Settings Screen]
        StS[Statistics Screen]
        AN[App Navigation]
    end
    
    subgraph "Business Logic Layer"
        TP[Transaction Provider]
        ThP[Theme Provider]
    end
    
    subgraph "Service Layer"
        subgraph "Parsing Module"
            MPS[MlKitParserService]
            FPS[FallbackParserService]
            LAS[LearnedAssociationService]
        end
        LS[LocalizationService]
        STS[Storage Service]
        CUtils[CurrencyUtils]
        AR[Audio Recorder]
        FU[File Upload]
        IU[Image Upload]
        VR[Video Recorder]
        AU[Amount Utils]
        RNF[Raw Number Finder]
    end
    
    subgraph "Data Layer"
        TM[Transaction Model]
        PR[ParseResult Model]
        LD[LocalizationData Model]
        AC[AmountCandidate Model]
        SP[Shared Preferences]
    end
    
    ChS --> TP
    ChS --> MPS
    TP --> LAS

    MPS --> LAS
    MPS --> FPS
    MPS --> PR
    FPS --> LS

    LS --> LD
    LAS --> STS

    CS --> TP
    SS --> ThP
    SS --> STS
    StS --> TP
    
    TP --> STS
    TP --> TM
    ThP --> SP
    
    STS --> SP
```

## State Management

The application uses the Provider pattern for state management. This provides a simple and efficient way to manage and propagate state changes throughout the application.

```mermaid
graph TD
    subgraph "Widget Tree"
        RW[Root Widget - MultiProvider]
        ML[MoneyLoverChatApp]
        AN[AppNavigation]
        SC[Screen Components]
    end
    
    subgraph "Providers"
        TP[TransactionProvider]
        ThP[ThemeProvider]
        MPS_P[MlKitParserService Provider]
    end

    subgraph "Services (Singletons)"
        LAS_P[LearnedAssociationService]
        LS_P[LocalizationService]
        CFS[CategoryFinderService]
        FPS[FallbackParserService]
    end
    
    subgraph "Data Sources"
        STS[Storage Service]
        SP[Shared Preferences]
    end
    
    RW --> TP
    RW --> ThP
    RW --> MPS_P
    RW --> LAS_P
    RW --> LS_P
    RW --> ML
    ML --> AN
    AN --> SC
    
    SC -- Consumer --> TP
    SC -- Consumer --> ThP
    SC -- Consumer --> MPS_P
    
    TP --> STS
    STS --> SP
    ThP --> SP
```

## Data Flow - Unified Learning & Transaction Processing

The sequence of operations when a user enters a transaction has been significantly updated. The system now attempts to use learned user preferences before falling back to ML Kit or the localized regex parser.

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant MlKitParserService
    participant LearnedAssociationService
    participant TransactionProvider

    User->>ChatScreen: Enters text: "Dinner at The Local Bistro 50"
    ChatScreen->>MlKitParserService: parseTransaction(text)

    MlKitParserService->>LearnedAssociationService: getAssociation("dinner at the local bistro")

    alt Learned Association Found
        LearnedAssociationService-->>MlKitParserService: Return {type: "expense", categoryId: "food"}
        Note over MlKitParserService: Bypasses ML Kit & Regex. Creates complete transaction.
        MlKitParserService-->>ChatScreen: Return ParseResult(status=success)
        ChatScreen->>TransactionProvider: addTransaction(transaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved"
    else No Association Found
        LearnedAssociationService-->>MlKitParserService: Return null
        Note over MlKitParserService: Proceeds with existing ML Kit / Fallback flow.
        MlKitParserService-->>ChatScreen: Return ParseResult(status=needsCategory)
        ChatScreen->>User: Show "soft fail" UI (e.g., category picker)
        User->>ChatScreen: Selects "Food" category
        ChatScreen->>LearnedAssociationService: learn(text, categoryId: "food")
        ChatScreen->>TransactionProvider: addTransaction(updatedTransaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved & Learned"
    end
```

## File Structure and Responsibility

| Component | Primary File(s) | Responsibility |
|-----------|-----------------|----------------|
| UI Layer | `screens/*.dart`, `widgets/*.dart` | User interface and interaction |
| Navigation | `navigation/app_navigation.dart` | Screen routing and navigation |
| State Management | `models/transaction_model.dart`, `theme.dart` | App-wide state management |
| Services | `services/*.dart` | Business logic implementation |
| **Localization** | **`services/localization_service.dart`** | **Loads locale-specific keywords for parsing.** |
| Parsing Module | `services/parser/*.dart` | Hybrid ML transaction parsing, including learning. |
| **Learning** | **`services/parser/learned_association_service.dart`** | **Stores and retrieves user corrections for future parsing.** |
| Utilities | `utils/*.dart` | Helper functions (e.g., currency) |
| Data Models | `models/*.dart` | Data structure definitions |
| Storage | `services/storage_service.dart` | Data persistence |

## Design Patterns

- **Provider Pattern**: Used for state management across the application.
- **Service Layer Pattern**: Used to separate business logic from UI components.
- **Singleton Pattern**: Used for managing service instances.
- **Strategy Pattern**: The hybrid parser uses learned associations as its primary strategy, followed by ML Kit, and finally a localized regex-based fallback strategy.
- **Observer Pattern**: Implemented via Provider for reactive UI updates.