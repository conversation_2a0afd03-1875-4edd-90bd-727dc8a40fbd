# Chat-Based Transaction Entry

This document explains the chat-based transaction entry feature, which is a core functionality of the DreamFlow application.

**Feature Status**: Fully Implemented
**Primary Service**: `MlKitParserService`
**Learning System**: `LearnedAssociationService`

## Overview

The chat interface provides an intuitive, conversational way for users to enter financial transactions. Instead of filling out forms, users can type natural language statements like "Spent $25 on lunch today" or "200k vnd for travel," and the app will parse these into structured transaction data.

The system is designed to be a helpful assistant that asks for clarification when input is ambiguous rather than failing. **Crucially, it learns from every user correction, becoming progressively faster and more accurate over time.**

## User Flow

The user flow is designed to handle ambiguity gracefully, turning potential errors into a simple, interactive conversation that improves the system's intelligence.

```mermaid
graph TD
    A[User opens Chat Screen] --> B[User types transaction text]
    B --> C{Parser Service Processes Text}
    C --> D{Learned Association Found?}
    D -- Yes --> F[Transaction Saved Instantly]
    F --> G[Display Confirmation]
    
    D -- No --> E{Amount & Type Clear?}
    E -- Yes --> H{Category Clear?}
    H -- Yes --> I[Transaction Saved]
    I --> J[Correction is Learned]
    J --> G
    
    H -- No --> K[Show Category Picker]
    K --> L[User Selects Category]
    L --> I
    
    E -- No --> M[Show Type Disambiguation]
    M --> N[User Selects Type via Quick Reply]
    N --> H
```

## Components Involved

### UI Components
- `ChatScreen`: Main interface for the chat interaction.
- `TransactionMessage`: Displays a saved transaction in the chat log.
- `CategoryPickerDialog`: A dialog for manual category selection.
- `QuickReplyWidget`: Renders interactive buttons for disambiguation.

### Business Logic
- `TransactionProvider`: Manages transaction and chat message state. It also informs the learning service when a user manually edits a transaction.
- `MlKitParserService`: The primary service that orchestrates the intelligent parsing flow.
- `LearnedAssociationService`: The core of the app's memory. It stores and retrieves user preferences for transaction types and categories based on text patterns.
- `LocalizationService`: Provides locale-specific keywords and number formats to the fallback parser.

### Data Storage
- `StorageService`: Persists transaction data and, most importantly, the key-value store of learned associations via `SharedPreferences`.

## Implementation Details

### The Intelligent Parsing System

The core of the feature is the `MlKitParserService`, which uses a multi-stage process designed for accuracy, speed, and adaptability:

1.  **Check for Learned Associations**: Before any complex processing, the service consults the `LearnedAssociationService`. If the user has entered a similar transaction before and corrected it, the system uses the learned category and type instantly, bypassing all other steps. This makes parsing common transactions extremely fast and accurate.
2.  **ML Kit Entity Extraction**: If no learned association is found, the service uses Google's on-device ML Kit to identify key entities like money (amount and currency) and dates from the user's text, regardless of language.
3.  **Localized Fallback Parsing**: If ML Kit is unavailable or fails, the system seamlessly falls back to a custom `FallbackParserService`. This secondary parser is fully localizable; it uses regex patterns that are dynamically built from JSON resource files (`assets/l10n/`) corresponding to the user's device language.

### Unified Learning System

The system is designed to never forget a correction. Learning is triggered from two key user actions:

-   **Resolving a "Soft Fail"**: When the app asks for a transaction's type or category and the user provides it, that choice is immediately saved by the `LearnedAssociationService`. The next time the user enters a similar phrase, no prompt will be needed.
-   **Manual Transaction Edits**: If a user goes back and manually edits the category or type of any transaction, the `TransactionProvider` detects this change and instructs the `LearnedAssociationService` to update its memory with the new, corrected information. This is a powerful signal that ensures the app adapts to a user's evolving preferences.

### Code Example

Here's a conceptual overview of how the `ChatScreen` handles a parser result.

```dart
// In ChatScreen's _sendMessage method
Future<void> _handleParseResult(ParseResult result) async {
  switch (result.status) {
    case ParseStatus.success:
      // Transaction was parsed successfully, either by learning, ML, or regex.
      await provider.addTransaction(result.transaction, originalText);
      break;
    
    case ParseStatus.needsType:
      // Show quick replies to ask user for transaction type.
      // The user's choice will be passed to the LearnedAssociationService.
      _showTypeDisambiguation(result);
      break;

    case ParseStatus.needsCategory:
      // Show a dialog to ask user for the category.
      // The user's choice will be passed to the LearnedAssociationService.
      _showCategorySelection(result);
      break;

    case ParseStatus.failed:
      // Show a user-friendly error message
      _addSystemMessage(result.error ?? "I had trouble understanding that.");
      break;
  }
}
```

## Future Enhancements

1.  **Smarter Suggestions**: Predict categories and vendors as the user types.
2.  **Voice Input**: Add support for voice-to-text transaction entry.
3.  **Recurring Transactions**: Detect and automate the setup of recurring bills or income.
4.  **Expanded Learning**: Broaden the learning system to remember other attributes, such as notes or tags associated with specific vendors.