import 'package:flutter_test/flutter_test.dart';
import 'lib/utils/raw_number_finder.dart';
import 'lib/services/parser/mlkit_parser_service.dart';

import 'test/mocks/mock_storage_service.dart';
import 'test/mocks/mock_entity_extractor.dart';

bool _isEmbeddedInVendorName(String fullText, int start, int end) {
  // Validate positions
  if (start < 0 || end > fullText.length || start >= end) return false;

  // Check if the number has letters immediately before it (vendor name pattern)
  final beforeIndex = start - 1;
  final afterIndex = end;

  final hasLetterBefore = beforeIndex >= 0 &&
      RegExp(r'[A-Za-z]').hasMatch(fullText[beforeIndex]);
  final hasLetterAfter = afterIndex < fullText.length &&
      RegExp(r'[A-Za-z]').hasMatch(fullText[afterIndex]);

  // Consider it embedded if:
  // 1. Letters both before and after (like "Lux68City")
  // 2. Letters before and followed by space/end (like "Restaurant123 ")
  // 3. Multiple consecutive letters before (like "Restaurant123")
  if (hasLetterBefore && hasLetterAfter) {
    return true; // Classic embedded case like "Lux68City"
  }

  if (hasLetterBefore) {
    // Check if there are multiple letters before (indicating a vendor name)
    int letterCount = 0;
    for (int i = beforeIndex; i >= 0 && RegExp(r'[A-Za-z]').hasMatch(fullText[i]); i--) {
      letterCount++;
    }
    // If 3+ letters before the number, likely a vendor name
    return letterCount >= 3;
  }

  return false;
}

void main() {
  test('debug service behavior', () async {
  TestWidgetsFlutterBinding.ensureInitialized();

  final text = 'Payment 100.50.25 USD or 200 USD';
  final candidates = RawNumberFinder.findAllNumbers(text);

  print('Text: "$text"');
  print('Found ${candidates.length} candidates:');
  for (final candidate in candidates) {
    final isEmbedded = _isEmbeddedInVendorName(text, candidate.start, candidate.end);
    print('  ${candidate.amount} ${candidate.currency} at ${candidate.start}-${candidate.end} from "${candidate.sourceText}" - embedded: $isEmbedded');
  }

  // Test with actual service
  print('\nTesting with actual service:');
  MlKitParserService.resetInstance();
  final mockStorage = MockStorageService();
  await mockStorage.init();

  final mockExtractor = MockEntityExtractorFactory.createEmpty();
  final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

  final result = await service.parseTransaction(text);
  print('Service result: ${result.transaction.amount}');
  print('Service status: ${result.status}');
  print('Requires user input: ${result.requiresUserInput}');
  if (result.candidateAmounts != null) {
    print('Candidate amounts: ${result.candidateAmounts}');
  }
  });
}
